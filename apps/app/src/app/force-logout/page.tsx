"use client";

import usePrivyAuth from "@/hooks/usePrivyAuth";
import { toast } from "@/hooks/useToast";
import { useEffect } from "react";

const ForceLogout = () => {
  const { confirmLogout } = usePrivyAuth();

  useEffect(() => {
    localStorage.clear();
    toast({
      title: "Unauthorized, make sure to login again. Or insert your invite code before logging in if you have one.",
    });

    confirmLogout(true);
  }, []);

  return null;
};

export default ForceLogout;

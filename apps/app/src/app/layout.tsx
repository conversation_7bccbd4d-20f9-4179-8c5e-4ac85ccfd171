import "animate.css";
import "mapbox-gl/dist/mapbox-gl.css";

import { Toaster } from "@/components/ui/toaster";
import { nextLoaderConfig } from "@/utils/configs";
import AppWrapper from "@/components/AppWrapper";
import Providers from "@/contexts/Providers";
import NextTopLoader from "nextjs-toploader";

import "./globals.css";

export const viewport = {
  themeColor: "#070F11",
  viewport: "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no",
};

export const metadata = {
  title: "IbiCash",
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
        <meta name="theme-color" content="#070F11" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-touch-fullscreen" content="yes" />
        <link rel="apple-touch-icon" href="https://ik.imagekit.io/97q72hphb/Union.png?updatedAt=1744141959581" />
      </head>
      <body className="bg-primary-dark text-white overflow-y-auto overflow-x-hidden h-dvh w-full">
        <NextTopLoader {...nextLoaderConfig} />
        <Toaster />
        <Providers>
          <AppWrapper>{children}</AppWrapper>
        </Providers>
      </body>
    </html>
  );
}

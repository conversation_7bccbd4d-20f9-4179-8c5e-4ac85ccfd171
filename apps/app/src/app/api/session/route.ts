import { cookies } from 'next/headers';

const COOKIE_NAME = 'session-token';
const COOKIE_DOMAIN = '.ibi.cash';

export async function GET() {
  const cookieStore = cookies();
  const cookie = cookieStore.get(COOKIE_NAME);

  if (!cookie) {
    return new Response(JSON.stringify({ value: null }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  return new Response(JSON.stringify({ value: cookie.value }), {
    status: 200,
    headers: { 'Content-Type': 'application/json' },
  });
}

export async function POST(request: Request) {
  const { value } = await request.json();

  if (typeof value !== 'string') {
    return new Response(JSON.stringify({ error: 'Invalid value' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  return new Response(JSON.stringify({ success: true }), {
    status: 200,
    headers: {
      'Set-Cookie': `${COOKIE_NAME}=${value}; Path=/; HttpOnly; Secure; SameSite=Lax; Domain=${COOKIE_DOMAIN}; Max-Age=604800`,
      'Content-Type': 'application/json',
    },
  });
}

"use client";

import { Edit } from "lucide-react";
import React, { useState } from "react";

const Profile = () => {
  const [activeTab, setActiveTab] = useState("balances");

  const profileData = {
    name: "br<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
    address: "2wsDo...m37t",
    followers: 0,
    following: 0,
    createdCoins: 0,
    avatar: "🐸",
  };

  const balanceData = [
    {
      name: "Solana balance",
      symbol: "SOL",
      balance: "0.0030 SOL",
      value: "$0",
      icon: "⚪",
      network: "Solana",
    },
    {
      name: "Pump Tunes 🎵 🔊",
      symbol: "PUMP",
      balance: "4.813 PUMP",
      value: "$0",
      mcap: "$35.8K",
      icon: "🎵",
      network: "Solana",
    },
  ];

  const notificationsData = [
    {
      type: "follow",
      user: "mahn2w85s",
      action: "started following you",
      time: "2h ago",
      avatar: "🐸",
    },
    {
      type: "mention",
      user: "cryptohawk",
      action: "mentioned you in a comment",
      time: "5h ago",
      avatar: "🔥",
    },
    {
      type: "trade",
      user: "gaambler",
      action: "bought your token",
      time: "1d ago",
      avatar: "🎲",
    },
  ];

  const whoToFollowData = [
    { name: "mahn2w85s", followers: "2177 followers", avatar: "🐸" },
    { name: "cryptohawk", followers: "2165 followers", avatar: "🔥" },
    { name: "gaambler", followers: "2107 followers", avatar: "🎲" },
    { name: "anon", followers: "2088 followers", avatar: "🐸" },
    { name: "2x9za", followers: "2073 followers", avatar: "🐸" },
  ];

  return (
    <div className="min-h-screen bg-black/80 text-white overflow-y-auto">
      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content - Left Side */}
          <div className="lg:col-span-3">
            {/* Profile Header */}
            <div className="bg-black/80 rounded-2xl border border-gray-800/50 p-6 mb-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center text-2xl">
                    {profileData.avatar}
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold">{profileData.name}</h1>
                    <div className="flex items-center gap-2 text-gray-400">
                      <span className="text-sm">{profileData.address}</span>
                    </div>
                  </div>
                </div>
                <button className="bg-gray-700 hover:bg-gray-600 px-4 py-2 rounded-lg font-medium text-sm transition-colors flex items-center gap-2">
                  <Edit size={16} />
                  edit
                </button>
              </div>

              {/* Stats */}
              <div className="flex items-center gap-8">
                <div className="text-center">
                  <div className="text-xl font-bold">{profileData.followers}</div>
                  <div className="text-sm text-gray-400">followers</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold">{profileData.following}</div>
                  <div className="text-sm text-gray-400">following</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold">{profileData.createdCoins}</div>
                  <div className="text-sm text-gray-400">created coins</div>
                </div>
              </div>
            </div>

            {/* Tab Content */}
            <div className="bg-black/80 rounded-2xl border border-gray-800/50 p-6">
              {/* Tabs */}
              <div className="flex items-center gap-8 mb-6">
                {["balances", "notifications"].map((tab) => (
                  <button
                    key={tab}
                    className={`pb-3 px-1 font-medium transition-colors relative ${
                      activeTab === tab ? "text-white" : "text-gray-400 hover:text-gray-300"
                    }`}
                    onClick={() => setActiveTab(tab)}
                  >
                    {tab}
                    {activeTab === tab && <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-white"></div>}
                  </button>
                ))}
              </div>
              {activeTab === "balances" ? (
                <div className="space-y-4">
                  {/* Headers */}
                  <div className="grid grid-cols-12 gap-4 text-sm text-gray-400 font-medium pb-2 border-b border-gray-800">
                    <div className="col-span-4">coins</div>
                    <div className="col-span-3">mcap</div>
                    <div className="col-span-5">value</div>
                  </div>

                  {/* Balance Items */}
                  {balanceData.map((item, index) => (
                    <div
                      key={index}
                      className="grid grid-cols-12 gap-4 items-center py-4 hover:bg-black/60 rounded-lg px-2 transition-colors"
                    >
                      <div className="col-span-4 flex items-center gap-3">
                        <div className="w-12 h-12 bg-gray-800 rounded-full flex items-center justify-center text-lg">
                          {item.icon}
                        </div>
                        <div>
                          <div className="font-medium">{item.name}</div>
                          <div className="text-xs text-gray-400">{item.balance}</div>
                        </div>
                      </div>
                      <div className="col-span-3 font-medium">{item.mcap || "-"}</div>
                      <div className="col-span-5">
                        <div className="font-bold text-lg">{item.value}</div>
                        <div className="text-sm text-gray-400">-</div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {notificationsData.map((notification, index) => (
                    <div
                      key={index}
                      className="flex items-start gap-3 p-4 hover:bg-black/60 rounded-lg transition-colors"
                    >
                      <div className="w-12 h-12 bg-gray-800 rounded-full flex items-center justify-center text-lg">
                        {notification.avatar}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium">{notification.user}</span>
                          <span className="text-gray-400">{notification.action}</span>
                        </div>
                        <div className="text-sm text-gray-500">{notification.time}</div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Sidebar - Right Side */}
          <div className="lg:col-span-1">
            <div className="bg-black/80 rounded-2xl border border-gray-800/50 p-6">
              <h3 className="text-lg font-semibold mb-4">who to follow</h3>
              <div className="space-y-4">
                {whoToFollowData.map((person, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center">
                        {person.avatar}
                      </div>
                      <div>
                        <div className="font-medium text-sm">{person.name}</div>
                        <div className="text-[10px] text-gray-400">{person.followers}</div>
                      </div>
                    </div>
                    <button className="bg-green-600 hover:bg-green-700 px-3 py-1 rounded-lg text-sm font-medium transition-colors">
                      follow
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;

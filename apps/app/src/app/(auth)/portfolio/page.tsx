"use client";

import { Bar<PERSON>hart3, ChevronDown, Eye, Search, Send, Share2, TrendingDown, TrendingUp } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";
import { Line, LineChart, ResponsiveContainer, YAxis } from "recharts";

const CryptoWallet = () => {
  const [, setChartWidth] = useState(0);
  const [activeMainTab, setActiveMainTab] = useState("Pump");
  const [activeSubTab, setActiveSubTab] = useState("Positions");
  const chartContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      const width = entries[0].contentRect.width;
      setChartWidth(width);
    });

    if (chartContainerRef.current) {
      observer.observe(chartContainerRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const chartData = Array.from({ length: 100 }, (_, i) => ({
    value: 9534868 + Math.sin(i * 0.1) * 200000 + Math.random() * 100000 - 200000,
  }));

  const transactions = [
    {
      type: "Receive",
      date: "Jun 25",
      amount: "+21B AMERICA250",
      value: "+$21B AMERICA250",
      icon: "🔵",
      color: "text-green-400",
    },
    {
      type: "Send",
      date: "Jun 25",
      amount: "-50 assets",
      value: "-50 assets",
      icon: "🔴",
      color: "text-red-400",
    },
    {
      type: "Receive",
      date: "Jun 25",
      amount: "+733,060,974 WOOLLY",
      value: "+733,060,974 WOOLLY",
      icon: "🟤",
      color: "text-green-400",
    },
    {
      type: "Receive",
      date: "Jun 25",
      amount: "+9,266,939,025 WOOL...",
      value: "+9,266,939,025 WOOL...",
      icon: "🟤",
      color: "text-green-400",
    },
    {
      type: "Receive",
      date: "Jun 25",
      amount: "+0.0001 ETH",
      value: "+0.0001 ETH",
      icon: "⚡",
      color: "text-green-400",
    },
  ];

  const mockAssetsData = {
    Points: {
      Positions: [
        {
          name: "WhiteRock",
          symbol: "WHITE",
          network: "Ethereum",
          price: "$0.000728",
          balance: "10000000000 WHITE",
          value: "$7,256,240",
          change: "-8.81% ($701,238.46)",
          changeColor: "text-red-400",
          icon: "🟣",
        },
        {
          name: "MOO DENG",
          symbol: "MOO",
          network: "Ethereum",
          price: "$0.000026",
          balance: "30,001,118,586,546 MOO...",
          value: "$791,487.34",
          change: "+0.11% ($863.87)",
          changeColor: "text-green-400",
          icon: "🟤",
        },
        {
          name: "Kyber Network Crystal",
          symbol: "KNC",
          network: "Ethereum",
          price: "$0.304349",
          balance: "700,008,531 KNC",
          value: "$213,046.81",
          change: "-0.02% ($38.30)",
          changeColor: "text-red-400",
          icon: "🔷",
        },
      ],
      History: [
        {
          type: "Buy",
          date: "Jun 24",
          asset: "WHITE",
          amount: "+1000000 WHITE",
          value: "$728",
          icon: "🟣",
          color: "text-green-400",
        },
        {
          type: "Sell",
          date: "Jun 23",
          asset: "MOO",
          amount: "-500000 MOO",
          value: "$13",
          icon: "🟤",
          color: "text-red-400",
        },
        {
          type: "Buy",
          date: "Jun 22",
          asset: "KNC",
          amount: "+1000 KNC",
          value: "$304",
          icon: "🔷",
          color: "text-green-400",
        },
      ],
    },
    Pump: {
      Positions: [
        {
          name: "Ethereum",
          symbol: "ETH",
          network: "24 Networks",
          price: "$2,431.52",
          balance: "27.677 ETH",
          value: "$67,298.99",
          change: "+0.06% ($42.62)",
          changeColor: "text-green-400",
          icon: "⚡",
        },
        {
          name: "Moonkin",
          symbol: "MOO",
          network: "Ethereum",
          price: "$0.0000027",
          balance: "21,065,354,478,378 MOO...",
          value: "$56,795.72",
          change: "-0.20% ($6,451.55)",
          changeColor: "text-red-400",
          icon: "🌙",
        },
      ],
      History: [
        {
          type: "Pump",
          date: "Jun 25",
          asset: "ETH",
          amount: "+5 ETH",
          value: "$12,157",
          icon: "⚡",
          color: "text-green-400",
        },
        {
          type: "Dump",
          date: "Jun 24",
          asset: "MOO",
          amount: "-1000000 MOO",
          value: "$2.7",
          icon: "🌙",
          color: "text-red-400",
        },
      ],
    },
    Predict: {
      Positions: [
        {
          name: "Mirai",
          symbol: "MIRAI",
          network: "Ethereum",
          price: "$0.0000061",
          balance: "9,000,000,000 MIRAI",
          value: "$55,254.11",
          change: "+0.07% ($37.04)",
          changeColor: "text-green-400",
          icon: "🔮",
        },
        {
          name: "FROGGER",
          symbol: "FROGGER",
          network: "Ethereum",
          price: "$0.000285",
          balance: "190,000,000 FROGGER",
          value: "$54,079.18",
          change: "-8.17% ($120,079.09)",
          changeColor: "text-red-400",
          icon: "🐸",
        },
      ],
      History: [
        {
          type: "Predict",
          date: "Jun 25",
          asset: "MIRAI",
          amount: "Long +1000",
          value: "$6.10",
          icon: "🔮",
          color: "text-green-400",
        },
        {
          type: "Predict",
          date: "Jun 24",
          asset: "FROGGER",
          amount: "Short -500",
          value: "$142.50",
          icon: "🐸",
          color: "text-red-400",
        },
      ],
    },
  };

  const currentTabData = mockAssetsData[activeMainTab];

  return (
    <div className="bg-black/80 text-white h-screen overflow-y-auto">
      {/* Search Header */}
      <div className="bg-black/80 border-b border-gray-800/50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4 flex-1 max-w-2xl">
              <div className="relative flex-1">
                <input
                  type="text"
                  placeholder="Asset, wallet, domain or identity"
                  className="w-full bg-black/80 border border-gray-700 rounded-lg py-2.5 px-4 text-gray-300 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <Search className="absolute right-3 top-3 text-gray-500" size={18} />
              </div>
              <button className="bg-black/80 hover:bg-black/60 border border-gray-700 rounded-lg px-3 py-2.5 font-medium transition-colors">
                F
              </button>
            </div>

            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-blue-500 rounded-sm flex items-center justify-center">
                  <span className="text-white text-xs font-bold">1</span>
                </div>
                <ChevronDown size={16} className="text-gray-400" />
              </div>
              <button className="flex items-center gap-2 bg-black/80 hover:bg-black/60 border border-gray-700 rounded-lg px-3 py-2.5 font-medium transition-colors">
                USD
                <ChevronDown size={16} />
              </button>
              <button className="p-2 bg-black/80 hover:bg-black/60 border border-gray-700 rounded-lg transition-colors">
                <Eye size={18} />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Header */}
      <div className="border-b border-gray-800/50 bg-black/80">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <div className="w-8 h-8 bg-blue-400 rounded-sm"></div>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">vitalik.eth</h1>
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-3xl font-bold">$9,534,868</span>
                  <span className="text-red-400 font-medium">-5% ($504,341.75)</span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <button className="flex items-center gap-2 bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg font-medium transition-colors">
                <BarChart3 size={16} />
                Analyze
              </button>
              <button className="p-2 bg-black/80 hover:bg-black/60 rounded-lg transition-colors">
                <Share2 size={20} />
              </button>
              <button className="p-2 bg-black/80 hover:bg-black/60 rounded-lg transition-colors">
                <Send size={20} />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          {/* Performance Section - Left Column */}
          <div className="lg:col-span-2">
            <div className="bg-black/80 rounded-2xl border border-gray-800/50 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold flex items-center gap-2">
                  <TrendingUp className="text-green-400" size={20} />
                  Performance
                </h2>
                <div className="flex items-center gap-2">
                  {["1H", "1D", "1W", "1M", "1Y", "Max"].map((period) => (
                    <button
                      key={period}
                      className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                        period === "1D" ? "bg-green-500 text-white" : "bg-black/80 hover:bg-black/60 text-gray-300"
                      }`}
                    >
                      {period}
                    </button>
                  ))}
                </div>
              </div>

              <div className="mb-6">
                <div className="text-3xl font-bold mb-2">$9,534,868</div>
                <div className="flex items-center gap-2 text-red-400">
                  <TrendingDown size={16} />
                  <span>-5% ($504,341.75)</span>
                  <span className="text-gray-500 text-sm">$10,268,344.42</span>
                </div>
              </div>

              <div className="h-64" ref={chartContainerRef}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={chartData}>
                    <Line
                      type="monotone"
                      dataKey="value"
                      stroke="#22c55e"
                      dot={false}
                      strokeWidth={2}
                      filter="drop-shadow(0 0 6px rgba(34, 197, 94, 0.3))"
                    />
                    <YAxis domain={["dataMin - 100000", "dataMax + 100000"]} hide />
                  </LineChart>
                </ResponsiveContainer>
              </div>

              <div className="flex justify-between text-sm text-gray-500 mt-2">
                <span>$9,453,137.81</span>
                <span>All Networks</span>
              </div>
            </div>
          </div>

          {/* History Section - Right Column */}
          <div className="lg:col-span-1">
            <div className="bg-black/80 rounded-2xl border border-gray-800/50 p-4">
              <h2 className="text-xl font-semibold mb-4">History</h2>

              <div className="space-y-2">
                {transactions.map((tx, index) => (
                  <div
                    key={index}
                    className="flex items-start gap-3 p-2 rounded-lg hover:bg-black/60 transition-colors"
                  >
                    <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center text-sm">
                      {tx.icon}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-sm">{tx.type}</span>
                        <span className="text-xs text-gray-400">{tx.date}</span>
                      </div>
                      <div className={`text-xs font-medium ${tx.color} truncate`}>{tx.amount}</div>
                    </div>
                  </div>
                ))}
              </div>

              <button className="w-full mt-4 py-2 text-center bg-gray-800 hover:bg-gray-700 rounded-lg font-medium text-sm transition-colors">
                See all
              </button>
            </div>
          </div>
        </div>

        {/* New Assets Section */}
        <div className="bg-black/80 rounded-2xl border border-gray-800/50 p-6">
          {/* Main Tabs */}
          <div className="flex items-center mb-6">
            <div className="flex items-center gap-8">
              {["Pump", "Predict"].map((tab) => (
                <button
                  key={tab}
                  className={`pb-3 px-1 font-medium transition-colors relative ${
                    activeMainTab === tab ? "text-white" : "text-gray-400 hover:text-gray-300"
                  }`}
                  onClick={() => setActiveMainTab(tab)}
                >
                  {tab}
                  {activeMainTab === tab && <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-white"></div>}
                </button>
              ))}
            </div>

            <div className="flex-1"></div>

            {/* Sub Tabs - Radio Group Style */}
            <div className="flex items-center bg-gray-800 rounded-lg p-1">
              {["Positions", "History"].map((subTab) => (
                <button
                  key={subTab}
                  className={`px-4 py-2 rounded-md font-medium text-sm transition-colors ${
                    activeSubTab === subTab ? "bg-black text-white" : "text-gray-300 hover:text-white"
                  }`}
                  onClick={() => setActiveSubTab(subTab)}
                >
                  {subTab}
                </button>
              ))}
            </div>
          </div>

          {/* Assets Content */}
          {activeSubTab === "Positions" ? (
            <div className="space-y-2">
              {/* Headers */}
              <div className="grid grid-cols-12 gap-4 text-xs text-gray-400 font-medium pb-2 border-b border-gray-800">
                <div className="col-span-3">ASSET</div>
                <div className="col-span-2">PRICE</div>
                <div className="col-span-3">BALANCE</div>
                <div className="col-span-4">VALUE</div>
              </div>

              {/* Asset Rows */}
              {currentTabData.Positions.map((asset, index) => (
                <div
                  key={index}
                  className="grid grid-cols-12 gap-4 items-center py-2 hover:bg-black/60 rounded-lg px-2 transition-colors"
                >
                  <div className="col-span-3 flex items-center gap-2">
                    <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center text-sm">
                      {asset.icon}
                    </div>
                    <div>
                      <div className="font-medium text-sm">{asset.name}</div>
                      <div className="text-xs text-gray-400">🔵 {asset.network}</div>
                    </div>
                  </div>
                  <div className="col-span-2 font-medium text-sm">{asset.price}</div>
                  <div className="col-span-3 text-xs">{asset.balance}</div>
                  <div className="col-span-4">
                    <div className="font-bold text-sm">{asset.value}</div>
                    <div className={`text-xs ${asset.changeColor}`}>{asset.change}</div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-1">
              {currentTabData.History.map((tx, index) => (
                <div key={index} className="flex items-center gap-3 p-2 rounded-lg hover:bg-black/60 transition-colors">
                  <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center text-sm">
                    {tx.icon}
                  </div>
                  <div className="flex items-center justify-between flex-1 min-w-0">
                    <div className="flex items-center gap-4">
                      <span className="font-medium text-sm">{tx.type}</span>
                      <span className="text-xs text-gray-300">{tx.asset}</span>
                      <span className={`text-xs font-medium ${tx.color}`}>{tx.amount}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="text-xs text-gray-400">{tx.value}</span>
                      <span className="text-xs text-gray-400">{tx.date}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Show all button */}
          <button className="w-full mt-6 py-3 text-center bg-gray-800 hover:bg-gray-700 rounded-lg font-medium transition-colors">
            Show all assets
          </button>
        </div>
      </div>
    </div>
  );
};

export default CryptoWallet;

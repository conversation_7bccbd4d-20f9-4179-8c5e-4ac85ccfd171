"use client";

import IbiIcon from "@/components/IbiUi/IbiIcon";
import LeaderBoard from "@/components/Leaderboard";
import { Button } from "@/components/ui/button";
import useCode from "@/hooks/useCode";
import usePrivyAuth from "@/hooks/usePrivyAuth";
import { useToast } from "@/hooks/useToast";
import { usePoints } from "@/queries/hooks/usePoints";
import { InviteCode, InviteCodeUse } from "@/types";
import { copyToClipboard } from "@/utils";
import { shortenText } from "@/utils/formatting";
import { motion } from "framer-motion";
import React, { useState } from "react";

const seasons = [
  { id: "I", status: "complete", date: "Ended: May 28th" },
  { id: "II", status: "complete", date: "Started: July 8th" },
  { id: "III", status: "live", date: "Live now" },
];

const levels = [
  { points: "1K", level: 1 },
  { points: "10K", level: 2 },
  { points: "50K", level: 3 },
  { points: "100K", level: 4 },
  { points: "500K", level: 5 },
];

const ShareButtons = ({ inviteCode }: { inviteCode: Partial<InviteCode> | null }) => {
  const { toast } = useToast();

  if (!inviteCode?.code) {
    return null;
  }

  const copyReferralLink = (code: string | undefined) => {
    if (!code) return;

    const referralLink = `${window.location.origin}?inviteCode=${code}`;
    copyToClipboard(referralLink);
    toast({
      title: "Referral link copied to clipboard",
      description: "Share this link with friends to invite them to the platform",
    });
  };

  return (
    <div className="flex gap-2 mb-8">
      {inviteCode.code.split("").map((c, i) => {
        return (
          <button
            key={i}
            className="px-3 py-1.5 bg-[#0a0e15] border border-gray-800/50 rounded-md text-gray-400 hover:text-white transition-colors"
          >
            {c}
          </button>
        );
      })}

      <button
        className="w-9 flex items-center justify-center bg-[#0a0e15] border border-gray-800/50 rounded-md text-gray-400 hover:text-white transition-colors"
        onClick={() => copyReferralLink(inviteCode.code)}
        title="Copy referral link"
      >
        <IbiIcon icon="ph:link-simple" className="text-sm" />
      </button>
    </div>
  );
};

const InviteCodePage = () => {
  const { toast } = useToast();
  const { referralCode: inviteCode } = useCode();
  const { user } = usePrivyAuth();
  const { pointsEvents } = usePoints(user?.wallet?.address);
  const [activeTab, setActiveTab] = useState("invite");

  const copyReferralLink = (code: string | undefined) => {
    if (!code) return;

    const referralLink = `${window.location.origin}?inviteCode=${code}`;
    copyToClipboard(referralLink);
    toast({
      title: "Referral link copied to clipboard",
      description: "Share this link with friends to invite them to the platform",
    });
  };

  const points = pointsEvents?.data?.data?.[0]?.amount ?? 0;

  const profileData = {
    name: shortenText(user?.wallet?.address as string, 12),
    address: user?.wallet?.address || "",
    followers: 0,
    following: 0,
    createdCoins: 0,
    avatar: "🎫",
    points: points,
    rank: 1444,
  };

  const rewardsData = [
    {
      name: "Membership",
      description: "Variable points",
      icon: "ph:crown",
      iconColor: "text-purple-400",
      bgColor: "bg-purple-500/10",
      status: "Auto-claimed",
      statusColor: "text-purple-400",
    },
    {
      name: "Reserve an Agent Name Service NFT",
      description: "5,000 points",
      icon: "ph:ticket",
      iconColor: "text-blue-400",
      bgColor: "bg-blue-500/10",
      status: "Claim",
      statusColor: "text-white",
      action: true,
    },
  ];

  return (
    <div className="min-h-screen bg-black/80 text-white overflow-y-auto">
      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 xl:grid-cols-5 gap-8">
          {/* Main Content - Left Side */}
          <div className="xl:col-span-3">
            {/* Profile Header */}
            <div className="bg-black/80 rounded-2xl border border-gray-800/50 p-6 mb-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center text-2xl">
                    {profileData.avatar}
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold">{profileData.name}</h1>
                    <div className="flex items-center gap-2 text-gray-400">
                      <span className="text-sm">{shortenText(profileData.address, 12)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Stats */}
              <div className="flex items-center gap-8">
                <div className="text-center">
                  <div className="text-xl font-bold">{profileData.points}</div>
                  <div className="text-sm text-gray-400">points</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold flex items-center gap-1">
                    <IbiIcon icon="game-icons:rank-3" className="text-sm" />
                    {profileData.rank}
                  </div>
                  <div className="text-sm text-gray-400">rank</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold">{inviteCode?.totalUses || 0}</div>
                  <div className="text-sm text-gray-400">invites</div>
                </div>
              </div>
            </div>

            {/* Tab Content */}
            <div className="bg-black/80 rounded-2xl border border-gray-800/50 p-6">
              {/* Tabs */}
              <div className="flex items-center gap-8 mb-6">
                {["invite", "seasons", "rewards"].map((tab) => (
                  <button
                    key={tab}
                    className={`pb-3 px-1 font-medium transition-colors relative capitalize ${
                      activeTab === tab ? "text-white" : "text-gray-400 hover:text-gray-300"
                    }`}
                    onClick={() => setActiveTab(tab)}
                  >
                    {tab}
                    {activeTab === tab && <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-white"></div>}
                  </button>
                ))}
              </div>

              {activeTab === "invite" && (
                <div className="space-y-6">
                  <div>
                    <h2 className="text-xl font-bold mb-2 text-white">Invite your friends!</h2>
                    <p className="text-sm text-gray-400 mb-6">
                      Earn 200 points and an additional 10% of their points.
                    </p>

                    {inviteCode ? (
                      <ShareButtons inviteCode={inviteCode} />
                    ) : (
                      <div className="mb-8 text-gray-400">No invite code available to share</div>
                    )}

                    {/* Progress Bar */}
                    <div className="mb-8">
                      <div className="flex justify-between items-end mb-2">
                        {levels.map((level) => (
                          <div key={level.level} className="flex flex-col items-center">
                            <div className="w-4 h-4 bg-[#010303] border border-gray-800 rounded-full flex items-center justify-center">
                              <div className="w-2 h-2 bg-gray-700 rounded-full" />
                            </div>
                            <p className="text-xs text-gray-500 mt-2">LEVEL {level.level}</p>
                            <p className="text-[10px] text-gray-700">{level.points}</p>
                          </div>
                        ))}
                      </div>
                      <div className="w-full h-[2px] bg-gray-800 relative" />
                    </div>

                    {/* Invite Code Details */}
                    <div className="bg-[#0a0e15] border border-gray-800/50 rounded-lg p-6">
                      <div className="flex justify-between items-center mb-4">
                        <h3 className="text-lg font-semibold text-white">Your Invite Code</h3>
                        {inviteCode && <span className="text-sm text-gray-400">Total Uses: {inviteCode.totalUses || 0}</span>}
                      </div>

                      <p className="text-gray-400 mb-6">
                        Share your invite code with friends to invite them to the platform. You will earn points when they
                        join.
                      </p>

                      {inviteCode ? (
                        <div className="space-y-6">
                          <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center justify-between p-4 rounded-lg bg-[#010303] border border-gray-800/30"
                          >
                            <div className="flex items-center gap-3">
                              <IbiIcon icon="mdi:ticket" className="text-2xl text-white" />
                              <div>
                                <p className="font-medium text-white">{inviteCode.code || "N/A"}</p>
                                <p className="text-sm text-gray-400">
                                  Created:{" "}
                                  {inviteCode.createdAt ? new Date(inviteCode.createdAt).toLocaleDateString() : "N/A"}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center gap-3">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-white hover:text-blue-400 hover:bg-blue-500/10"
                                onClick={() => copyReferralLink(inviteCode.code)}
                              >
                                <IbiIcon icon="iconamoon:copy" className="text-lg mr-2" />
                                Copy Link
                              </Button>
                            </div>
                          </motion.div>

                          {inviteCode && inviteCode.uses && inviteCode.uses.length > 0 && (
                            <>
                              <h4 className="text-md font-semibold text-white mt-8 mb-4">Usage History</h4>
                              <div className="space-y-4">
                                {(inviteCode.uses || []).map((use: InviteCodeUse) => (
                                  <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    key={use.id}
                                    className="flex items-center justify-between p-4 rounded-lg bg-[#010303] border border-gray-800/30"
                                  >
                                    <div className="flex items-center gap-3">
                                      <IbiIcon icon="mdi:ticket-confirmation" className="text-2xl text-white" />
                                      <div>
                                        <p className="font-medium text-white">
                                          Used by: {use.userWallet ? shortenText(use.userWallet, 12) : "Unknown"}
                                        </p>
                                        <p className="text-sm text-gray-400">
                                          Date: {use.usedAt ? new Date(use.usedAt).toLocaleDateString() : "N/A"}
                                        </p>
                                      </div>
                                    </div>
                                    <span className="text-sm font-semibold text-yellow-600">Used</span>
                                  </motion.div>
                                ))}
                              </div>
                            </>
                          )}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <div className="animate-pulse">Loading invite code...</div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {activeTab === "seasons" && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    {seasons.map((season) => (
                      <div
                        key={season.id}
                        className="bg-[#0a0e15] border border-gray-800/50 rounded-lg p-4 sm:p-6 relative overflow-hidden"
                      >
                        <div className="flex justify-between items-start mb-8">
                          <h3 className="text-lg font-semibold text-white">Season {season.id}</h3>
                          <p className="text-xs text-gray-500">{season.date}</p>
                        </div>
                        <div className="flex items-center gap-2 text-sm relative z-10">
                          {season.status === "live" ? (
                            <div className="flex items-center gap-1 text-purple-400">
                              <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" />
                              <span>Live now</span>
                            </div>
                          ) : (
                            <div className="flex items-center gap-1 text-green-400">
                              <IbiIcon icon="ph:check" />
                              <span>Complete</span>
                            </div>
                          )}
                        </div>
                        <div
                          className="absolute bottom-0 right-0 w-32 h-32 bg-purple-500/5 rounded-full blur-2xl"
                          style={{
                            transform: "translate(25%, 25%)",
                          }}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === "rewards" && (
                <div className="space-y-4">
                  {rewardsData.map((reward, index) => (
                    <div
                      key={index}
                      className="bg-[#0a0e15] border border-gray-800/50 rounded-lg relative overflow-hidden"
                    >
                      <div className="p-6 flex items-center justify-between relative z-10">
                        <div className="flex items-center gap-3">
                          <div className={`p-2 ${reward.bgColor} rounded-lg`}>
                            <IbiIcon icon={reward.icon} className={reward.iconColor} />
                          </div>
                          <div>
                            <h3 className="font-medium text-white">{reward.name}</h3>
                            <p className="text-sm text-gray-400">{reward.description}</p>
                          </div>
                        </div>
                        {reward.action ? (
                          <Button size="sm" className="text-white border-gray-700 hover:bg-gray-800">
                            {reward.status}
                          </Button>
                        ) : (
                          <span className={`text-sm ${reward.statusColor}`}>{reward.status}</span>
                        )}
                      </div>
                      <div
                        className="absolute bottom-0 right-0 w-32 h-32 bg-purple-500/5 rounded-full blur-2xl"
                        style={{
                          transform: "translate(25%, 25%)",
                        }}
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Sidebar - Right Side */}
          <div className="xl:col-span-2">
            <div className="bg-black/80 rounded-2xl border border-gray-800/50 p-6">
              <LeaderBoard />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InviteCodePage;

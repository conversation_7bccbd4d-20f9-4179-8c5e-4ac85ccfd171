import { api } from "@/lib/axios";
import { IValidateCodeResponse, InviteCode } from "@/types";
import axios from "axios";

export const authService = {
  validateCode: async (code: string) => {
    try {
      const response = await api.post<IValidateCodeResponse>("/api/referrals/validate", { code });
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        return error.response.data as IValidateCodeResponse;
      }
      throw new Error("An unexpected error occurred");
    }
  },

  getReferralCode: () => api.get<InviteCode>("/api/referrals/code"),

  getSession: async () => {
    const response = await fetch("/api/session");
    return response.json();
  },
  setSession: async (session: string) => {
    const response = await fetch("/api/session", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ value: session }),
    });
    return response.json();
  },
} as const;

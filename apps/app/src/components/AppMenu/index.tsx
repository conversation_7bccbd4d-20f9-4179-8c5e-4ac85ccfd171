import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger } from "@/components/ui/sheet";
import usePrivyAuth from "@/hooks/usePrivyAuth";
import { ABC_WHITE_PLUS_BOLD, ABC_WHITE_PLUS_LIGHT } from "@/utils/configs";
import { User } from "@privy-io/react-auth";
import { usePathname } from "next/navigation";
import { useRouter } from "nextjs-toploader/app";
import { useState } from "react";

import { shortenText } from "../../utils/formatting";
import { useAppHeaderSelector } from "../Headers/EntryHeader/header.store";
import IbiIcon from "../IbiUi/IbiIcon";
import { Button } from "../ui/button";

const AppMenu = () => {
  const { user } = usePrivyAuth();
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const pathname = usePathname();

  const toggleConfirmLogout = useAppHeaderSelector.use.toggleConfirmLogout();

  const handleRouterChange = (route: string) => {
    router.push(route);
    setOpen(false);
  };

  const highlighted = (route: string) => (pathname === route ? "bg-white text-black" : "");
  const menuOptions = [
    {
      name: "Claim",
      route: "/claim",
      icon: "subway:world-1",
    },
    {
      name: "Predict",
      route: "/predict",
      icon: "subway:water-1",
    },
    {
      name: "Portfolio",
      route: "/portfolio",
      icon: "ri:dashboard-fill",
    },
    {
      name: "Profile",
      route: "/profile",
      icon: "gg:profile",
    },
    {
      name: "Manage accounts",
      route: "/manage-accounts",
      icon: "mdi:accounts-group",
    },
    {
      name: "Invite codes",
      route: "/invite-codes",
      icon: "ri:share-box-line",
    },
    {
      name: "Settings",
      route: "/settings",
      icon: "material-symbols:settings-outline",
    },
    {
      name: "About page",
      route: "/",
      icon: "pepicons-pop:tree-circle-filled",
    },
  ];

  return (
    <div onClick={(e) => e.stopPropagation()}>
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetTrigger asChild>
          <IbiIcon
            icon="ion:reorder-two-outline"
            className="text-3xl opacity-60 hover:opacity-100 bg-transparent cursor-pointer"
          />
        </SheetTrigger>
        <SheetContent side="left" className="bg-[#000] w-[280px] border-0 flex flex-col">
          <SheetHeader>
            <SheetTitle className="text-white">
              <CurrentUser user={user as User} />
            </SheetTitle>
          </SheetHeader>
          <div className="flex flex-col justify-between flex-1">
            <div>
              {menuOptions.map((option) => (
                <Button
                  key={option.name}
                  size="full"
                  className={`bg-inherit flex justify-start shadow-none gap-2 hover:bg-white hover:text-black ${highlighted(option.route)}`}
                  onClick={() => handleRouterChange(option.route)}
                >
                  <IbiIcon icon={option.icon} />
                  <span style={ABC_WHITE_PLUS_LIGHT.style}>{option.name}</span>
                </Button>
              ))}
            </div>
            <Button
              size="full"
              className={`bg-inherit flex justify-start shadow-none gap-2 hover:bg-white hover:text-black`}
              onClick={toggleConfirmLogout}
            >
              <IbiIcon icon="el:off" />
              <span style={ABC_WHITE_PLUS_LIGHT.style}>Exit</span>
            </Button>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
};

const CurrentUser = ({ user }: { user: User }) => {
  const serviceMap = {
    github: { name: user?.github?.name, icon: "fontisto:github" },
    google: { name: user?.google?.name, icon: "logos:google-icon" },
    apple: { name: user?.apple?.email, icon: "grommet-icons:apple" },
    discord: { name: user?.discord?.username, icon: "skill-icons:discord" },
    twitter: { name: user?.twitter?.username, icon: "akar-icons:twitter-fill" },
    wallet: { name: shortenText(user?.wallet?.address as string, 12), icon: "iconoir:wallet" },
  };

  const service = Object.entries(serviceMap).find(([, value]) => value.name);
  if (service) {
    return <UserViewWrapper name={service[1].name ?? ""} icon={service[1].icon} />;
  }
};

const UserViewWrapper = ({ name, icon }: { name: string; icon: string }) => {
  return (
    <div className="flex items-center gap-3">
      <IbiIcon icon={icon} className="text-2xl" />
      <div className="flex flex-col gap-1">
        <h1 style={ABC_WHITE_PLUS_BOLD.style} className="text-sm font-semibold">
          {name}
        </h1>
        <p style={ABC_WHITE_PLUS_LIGHT.style} className="text-xs font-normal">
          <strong>10</strong> points
        </p>
      </div>
    </div>
  );
};

export default AppMenu;

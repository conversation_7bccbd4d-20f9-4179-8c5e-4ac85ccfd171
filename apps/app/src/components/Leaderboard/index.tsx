import IbiIcon from "@/components/IbiUi/IbiIcon";
import { usePoints } from "@/queries/hooks/usePoints";
import { shortenText } from "@/utils/formatting";
import React from "react";

const LeaderBoard = () => {
  const { leaderboard } = usePoints();
  const { data, isLoading, currentPage, handleNextPage, handlePreviousPage } = leaderboard;

  if (isLoading) {
    return (
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-white">Leaderboard</h2>
        <div className="space-y-2">
          {[...Array(10)].map((_, i) => (
            <div key={i} className="flex items-center justify-between p-3 bg-[#010303]/30 rounded-lg">
              <div className="flex items-center gap-2">
                <div className="w-6 h-4 bg-gray-800 rounded animate-pulse" />
                <div className="w-28 h-4 bg-gray-800 rounded animate-pulse" />
              </div>
              <div className="w-20 h-4 bg-gray-800 rounded animate-pulse" />
            </div>
          ))}
        </div>

        <div className="flex justify-end gap-2 mt-4 items-center">
          <div className="w-8 h-8 bg-gray-800 rounded animate-pulse" />
          <div className="w-24 h-4 bg-gray-800 rounded animate-pulse" />
          <div className="w-8 h-8 bg-gray-800 rounded animate-pulse" />
        </div>
      </div>
    );
  }

  if (!data) return null;

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold text-white">Leaderboard</h2>
      <div className="space-y-4">
        {data.data.map((item, index) => (
          <div
            key={`${item.address}-${index}`}
            className="flex items-center justify-between py-4 px-3 bg-[#010303]/30 rounded-lg"
          >
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-400">#{index + 1}</span>
              <span className="text-white">{shortenText(item.address, 12)}</span>
            </div>
            <span className="text-white font-medium">{item.points} points</span>
          </div>
        ))}
      </div>

      <div className="flex justify-end gap-2 mt-4 items-center">
        <button
          disabled={!data.meta.hasPreviousPage}
          onClick={handlePreviousPage}
          className="p-2 text-sm text-gray-400 hover:text-white disabled:opacity-50"
        >
          <IbiIcon icon="lucide:chevron-left" />
        </button>
        <span className="text-gray-400">
          Page {currentPage} of {data.meta.pageCount}
        </span>
        <button
          disabled={!data.meta.hasNextPage}
          onClick={handleNextPage}
          className="p-2 text-sm text-gray-400 hover:text-white disabled:opacity-50"
        >
          <IbiIcon icon="lucide:chevron-right" />
        </button>
      </div>
    </div>
  );
};

export default LeaderBoard;

"use client";

import useAuthentication from "@/hooks/useAuthentication";
import usePrivyAuth from "@/hooks/usePrivyAuth";
import { handleStorage } from "@/utils/storage";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";

import EntryLoading from "../EntryLoading";
import { useAppHeaderSelector } from "../Headers/EntryHeader/header.store";
import IbiConfirmDialog from "../IbiUi/IbiConfirmDialog";

const AppWrapper = ({ children }: { children: React.ReactNode }) => {
  const { ready = false, authenticated, onLogin, confirmLogout: setConfirmLogout, isOpen } = usePrivyAuth();
  const { isFullyAuthenticated } = useAuthentication();
  const pathname = usePathname();
  const router = useRouter();
  const searchParams = useSearchParams();

  const confirmLogout = useAppHeaderSelector.use.confirmLogout();
  const toggleConfirmLogout = useAppHeaderSelector.use.toggleConfirmLogout();

  useEffect(() => {
    if (ready) {
      const pathHistory = pathname;
      handleStorage("session", "pathHistory", "create", pathHistory);

      if (!isOpen && pathname === "/" && !authenticated) {
        onLogin();
      }

      // Only redirect to /claim when user is fully authenticated (Privy + Backend validation)
      if (isFullyAuthenticated && pathname === "/") {
        const inviteCodeFromURL = searchParams?.get("inviteCode")
          ? `?inviteCode=${searchParams?.get("inviteCode")}`
          : "";
        router.push(`/claim${inviteCodeFromURL}`);
      }
    }
  }, [ready, authenticated, isOpen, isFullyAuthenticated]);

  return (
    <EntryLoading wrapperClass="h-full" condition={ready}>
      <>
        <div className="w-full h-full flex bg-primary-dark min-h-screen overflow-y-hidden overflow-x-hidden">
          <div className="flex-1 h-full">{children}</div>
        </div>
        <IbiConfirmDialog
          title="Confirm logout"
          description="Are you sure you want to log out?"
          open={confirmLogout}
          onOpenChange={toggleConfirmLogout}
          onCancel={toggleConfirmLogout}
          onConfirm={() => {
            setConfirmLogout(true);
            toggleConfirmLogout();
          }}
        />
      </>
    </EntryLoading>
  );
};

export default AppWrapper;

"use client";

import usePrivyAuth from "@/hooks/usePrivyAuth";
import { LANDING_REDIRECT_URL } from "@/utils/constants";
import { Loader } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

const ReferralRedirectArea = () => {
  const { authenticated } = usePrivyAuth();
  const router = useRouter();

  useEffect(() => {
    if (!authenticated) {
      window.location.href = LANDING_REDIRECT_URL;
    } else {
      router.push("/claim");
    }
  }, [authenticated, router]);

  return (
    <section className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-950 via-gray-900 to-gray-950">
      <div className="relative">
        <div className="absolute -inset-10 bg-cyan-500/20 blur-3xl rounded-full animate-pulse"></div>
        <div className="absolute -inset-20 bg-indigo-500/10 blur-3xl rounded-full animate-pulse delay-75"></div>

        <div className="relative bg-gray-900/80 backdrop-blur-xl rounded-2xl border border-gray-800/50 p-10 flex flex-col items-center gap-6">
          <div className="relative">
            <div className="absolute inset-0 rounded-full border-t-2 border-r-2 border-cyan-500/30 animate-spin [animation-duration:3s]"></div>
            <div className="absolute inset-2 rounded-full border-t-2 border-l-2 border-indigo-500/30 animate-spin [animation-duration:2s]"></div>
            <Loader className="w-14 h-14 text-cyan-400 animate-spin relative" />
          </div>

          <div className="flex flex-col items-center gap-2">
            <h1 className="text-xl font-medium bg-gradient-to-r from-cyan-400 to-indigo-400 bg-clip-text text-transparent">
              Validating information
            </h1>
            <p className="text-gray-400 text-sm">Please wait while we process your request</p>
          </div>

          <div className="w-48 h-1 bg-gray-800 rounded-full overflow-hidden">
            <div className="h-full w-1/2 bg-gradient-to-r from-cyan-500 to-indigo-500 rounded-full animate-[loading_2s_ease-in-out_infinite]"></div>
          </div>
        </div>
      </div>

      <style>{`
        @keyframes loading {
          0% { transform: translateX(-100%); }
          50% { transform: translateX(100%); }
          100% { transform: translateX(-100%); }
        }
      `}</style>
    </section>
  );
};

export default ReferralRedirectArea;

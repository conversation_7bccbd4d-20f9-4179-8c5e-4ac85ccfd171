"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import React, { useEffect, useState } from "react";

import CreatePredictionForm from "../CreatePredictionForm";
import { PolygonFeature } from "../hooks/usePolygonDraw";

interface PolygonSidebarProps {
  polygonFeature: PolygonFeature | null;
  maxArea?: number;
  shouldClearFields?: boolean;
  onFieldsCleared?: () => void;
  onRegeneratePolygonId?: () => void;
  onPredictionCreated?: () => void;
}

const PolygonSidebar: React.FC<PolygonSidebarProps> = ({
  polygonFeature,
  maxArea = 15000,
  shouldClearFields,
  onFieldsCleared,
  onRegeneratePolygonId,
  onPredictionCreated,
}) => {
  const [, setSuccessMessage] = useState<string | null>(null);
  const [, setErrorMessage] = useState<string | null>(null);
  const [title, setTitle] = useState<string>("");
  const [description, setDescription] = useState<string>("");

  useEffect(() => {
    if (shouldClearFields) {
      setTitle("");
      setDescription("");
      onFieldsCleared?.();
    }
  }, [shouldClearFields, onFieldsCleared]);

  useEffect(() => {
    if (!polygonFeature) {
      setTitle("");
      setDescription("");
    }
  }, [polygonFeature]);

  const isAreaTooLarge = polygonFeature ? polygonFeature.properties.area_ha > maxArea : false;

  const handlePredictionSuccess = (txHash: string) => {
    setSuccessMessage(`Prediction created successfully! Transaction: ${txHash.slice(0, 10)}...`);
    setErrorMessage(null);
    setTimeout(() => setSuccessMessage(null), 5000);

    setTitle("");
    setDescription("");

    onPredictionCreated?.();
  };

  const handlePredictionError = (error: string) => {
    setErrorMessage(error);
    setSuccessMessage(null);

    setTitle("");
    setDescription("");

    onRegeneratePolygonId?.();
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value.length <= 100) {
      setTitle(value);
    }
  };

  const handleDescriptionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value.length <= 100) {
      setDescription(value);
    }
  };

  return (
    <div className="fixed top-1/2 right-4 transform -translate-y-1/2 w-96 h-[calc(100vh-120px)] text-xs rounded-xl bg-black/85 backdrop-blur-xl border-2 border-gray-700 shadow-lg overflow-hidden z-10">
      <div className="h-full flex flex-col">
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h1 className="text-[14px] font-bold">Weather Prediction</h1>
          <div className="flex flex-col items-center bg-green-900/50 text-green-400 rounded-lg px-2 py-0.5">
            <span>Status</span>
            <div className="flex items-center gap-1">
              <span>{polygonFeature ? "Ready" : "Waiting"}</span>
            </div>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto p-4">
          {polygonFeature ? (
            <div className="space-y-4">
              {isAreaTooLarge && (
                <div className="mb-4 p-3 bg-red-900/30 border border-red-700/50 rounded-lg">
                  <p className="text-red-400 text-xs">
                    <strong>Warning:</strong> Area too large. Must be below {maxArea.toLocaleString()} ha.
                  </p>
                </div>
              )}

              {!isAreaTooLarge && (
                <div className="space-y-4">
                  <div className="space-y-3 p-3 bg-black/85 rounded-lg border border-gray-700">
                    <div className="space-y-2">
                      <Label htmlFor="title" className="text-sm text-gray-300">
                        Title<span className="text-red-400">*</span>
                      </Label>
                      <div className="space-y-1">
                        <Input
                          id="title"
                          type="text"
                          value={title}
                          onChange={handleTitleChange}
                          placeholder="Enter prediction title..."
                          maxLength={100}
                          className="bg-gray-900 border-gray-600 text-white text-sm"
                        />
                        <div className="text-xs text-gray-500 text-right">{title.length}/100</div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description" className="text-sm text-gray-300">
                        Description
                      </Label>
                      <div className="space-y-1">
                        <Input
                          id="description"
                          value={description}
                          onChange={handleDescriptionChange}
                          placeholder="Enter prediction description..."
                          maxLength={100}
                          className="bg-gray-900 border-gray-600 text-white text-sm"
                        />
                        <div className="text-xs text-gray-500 text-right">{description.length}/100</div>
                      </div>
                    </div>
                  </div>

                  <CreatePredictionForm
                    polygonFeature={polygonFeature}
                    onSuccess={handlePredictionSuccess}
                    onError={handlePredictionError}
                    title={title}
                    description={description}
                    shouldClearForm={shouldClearFields}
                    onFormCleared={onFieldsCleared}
                  />
                </div>
              )}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-center">
              <div className="w-12 h-12 border-2 border-gray-600 border-dashed rounded-lg mx-auto mb-4 flex items-center justify-center">
                <div className="w-4 h-4 bg-gray-600 rounded"></div>
              </div>
              <p className="text-gray-500 text-sm">Awaiting polygon creation...</p>
              <p className="text-gray-600 text-xs mt-2">Click the &quot;Polygon&quot; button to start drawing</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PolygonSidebar;

import usePrivyAuth from "@/hooks/usePrivyAuth";
import axios from "axios";
import { useCallback, useEffect, useState } from "react";

export interface Prediction {
  id: string;
  title: string;
  description: string;
  eventType: string;
  eventConfig: {
    unit: string;
    operator: string;
    threshold: number;
  };
  startTime: string;
  endTime: string;
  createdAt: string;
  status: string;
}

export interface PolygonFeature {
  type: "Feature";
  geometry: {
    type: "Polygon";
    coordinates: number[][][];
  };
  properties: {
    polygonId: string;
    polygonArea: number;
    predictions: Prediction[];
  };
}

export interface PredictionFeature {
  type: "Feature";
  geometry: {
    type: "Polygon";
    coordinates: number[][][];
  };
  properties: {
    id: string;
    polygonId: string;
    title: string;
    areaHa: number;
    description: string;
    eventType: string;
    eventConfig: {
      unit: string;
      operator: string;
      threshold: number;
    };
    startDate: string;
    endDate: string;
    status: string;
    createdAt: string;
  };
}

export interface PolygonsResponse {
  type: "FeatureCollection";
  features: PolygonFeature[];
}

export const usePredictions = () => {
  const { getJWT } = usePrivyAuth();
  const [polygons, setPolygons] = useState<PolygonFeature[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPredictions = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.get<PolygonsResponse>("https://api.yby.energy/api/predictions/geojson", {
        headers: {
          Authorization: `Bearer ${await getJWT()}`,
        },
      });

      setPolygons(response.data.features);
    } catch (err) {
      console.error("Error fetching predictions:", err);
      setError("Failed to fetch predictions");
    } finally {
      setLoading(false);
    }
  }, [getJWT]);

  useEffect(() => {
    fetchPredictions();
  }, [fetchPredictions]);

  const refetchPredictions = useCallback(() => {
    fetchPredictions();
  }, [fetchPredictions]);

  return {
    polygons,
    predictions: polygons,
    loading,
    error,
    refetchPredictions,
  };
};

"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { shortenText } from "@/utils/formatting";
import { Droplets, MapPin, Plus, Thermometer, Wind, X } from "lucide-react";
import React, { useState } from "react";

import { PolygonFeature, Prediction } from "../hooks/usePredictions";

// import PredictionDetailModal from "./PredictionDetailModal";

interface PredictionDetailsSidebarProps {
  polygon: PolygonFeature | null;
  onClose: () => void;
  onCreatePrediction?: (polygonId: string) => void;
  onBuyTokens?: (predictionAddress: string, outcomeIndex: number, amount: string) => void;
  isLoading?: boolean;
}

const PredictionDetailsSidebar: React.FC<PredictionDetailsSidebarProps> = ({
  polygon,
  onClose,
  onCreatePrediction,
  // onBuyTokens,
  isLoading = false,
}) => {
  const [, setSelectedPrediction] = useState<Prediction | null>(null);

  if (!polygon) return null;

  const getParsedPredictions = (): Prediction[] => {
    try {
      const predictions = polygon?.properties?.predictions;
      if (!predictions) return [];

      if (Array.isArray(predictions)) {
        return predictions;
      }

      if (typeof predictions === "string") {
        return JSON.parse(predictions);
      }

      return [];
    } catch (error) {
      console.error("Error parsing predictions:", error);
      return [];
    }
  };

  const parsedPredictions = getParsedPredictions();

  const getWeatherIcon = (eventType: string) => {
    switch (eventType) {
      case "rainfall":
        return <Droplets className="h-4 w-4" />;
      case "temperature":
        return <Thermometer className="h-4 w-4" />;
      case "wind":
        return <Wind className="h-4 w-4" />;
      default:
        return <MapPin className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    if (!status) return "bg-gray-900/50 text-gray-400 border-gray-700";

    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-900/50 text-green-400 border-green-700";
      case "completed":
        return "bg-gray-900/50 text-gray-400 border-gray-700";
      case "expired":
        return "bg-red-900/50 text-red-400 border-red-700";
      default:
        return "bg-gray-900/50 text-gray-400 border-gray-700";
    }
  };

  const getEventTypeColor = (eventType: string) => {
    switch (eventType) {
      case "rainfall":
        return "border-blue-500";
      case "temperature":
        return "border-orange-500";
      case "wind":
        return "border-green-500";
      default:
        return "border-gray-700";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getEventConfigText = (eventType: string, eventConfig: any) => {
    if (!eventConfig) return "N/A";

    switch (eventType) {
      case "rainfall":
        return `${eventConfig.operator === "gte" ? "≥" : "≤"} ${eventConfig.threshold}${eventConfig.unit}`;
      case "temperature":
        if (eventConfig.minTemp !== undefined && eventConfig.maxTemp !== undefined) {
          return `${eventConfig.minTemp}°${eventConfig.unit} - ${eventConfig.maxTemp}°${eventConfig.unit}`;
        } else if (eventConfig.threshold !== undefined) {
          return `${eventConfig.threshold}°${eventConfig.unit}`;
        }
        return "N/A";
      case "wind":
        if (eventConfig.maxSpeed !== undefined) {
          return `${eventConfig.operator === "gte" ? "≥" : "≤"} ${eventConfig.maxSpeed} ${eventConfig.unit}`;
        } else if (eventConfig.threshold !== undefined) {
          return `${eventConfig.operator === "gte" ? "≥" : "≤"} ${eventConfig.threshold} ${eventConfig.unit}`;
        }
        return "N/A";
      default:
        return "N/A";
    }
  };

  const handleCreatePrediction = () => {
    if (onCreatePrediction) {
      onCreatePrediction(polygon.properties.polygonId);
    }
  };

  const handlePredictionClick = (prediction: Prediction) => {
    setSelectedPrediction(prediction);
  };

  // const handleCloseModal = () => {
  //   setSelectedPrediction(null);
  // };

  // const handleBuyTokens = (predictionAddress: string, outcomeIndex: number, amount: string) => {
  //   if (onBuyTokens) {
  //     onBuyTokens(predictionAddress, outcomeIndex, amount);
  //   }
  // };

  return (
    <div className="fixed top-1/2 right-4 transform -translate-y-1/2 w-96 h-[calc(100vh-120px)] text-xs rounded-xl bg-black/85 backdrop-blur-xl border-2 border-gray-700 shadow-lg overflow-hidden z-10">
      <div className="h-full flex flex-col">
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h1 className="text-[14px] font-bold">Polygon Predictions</h1>
          <div className="flex items-center gap-2">
            <Badge className="bg-gray-900/50 text-gray-400 border-gray-700 text-xs">
              {parsedPredictions.length} prediction{parsedPredictions.length !== 1 ? "s" : ""}
            </Badge>
            <Button variant="ghost" size="sm" onClick={onClose} className="h-6 w-6 p-0 hover:bg-gray-700">
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto p-4">
          <div className="space-y-4">
            <Card className="bg-black/85 border-gray-700">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm text-white flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-gray-400" />
                  Polygon Information
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Polygon ID:</span>
                    <span className="text-white text-xs font-mono">
                      {shortenText(polygon.properties.polygonId, 20)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Area:</span>
                    <span className="text-white">{polygon.properties.polygonArea.toFixed(2)} ha</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {parsedPredictions.length === 0 ? (
              <Card className="bg-black/85 border-gray-700">
                <CardContent className="p-4 text-center">
                  <p className="text-gray-400">No predictions for this polygon yet.</p>
                  <p className="text-gray-500 text-xs mt-2">Create your first prediction using the button below.</p>
                </CardContent>
              </Card>
            ) : (
              parsedPredictions.map((prediction: Prediction) => (
                <Card
                  key={prediction.id}
                  className={`bg-black/85 border-2 ${getEventTypeColor(prediction.eventType)} cursor-pointer hover:bg-gray-900/50 transition-colors`}
                  onClick={() => handlePredictionClick(prediction)}
                >
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-2">
                      <Badge className={`${getStatusColor(prediction.status)} text-xs`}>{prediction.status}</Badge>
                      <div className="flex items-center gap-1">
                        {getWeatherIcon(prediction.eventType)}
                        <span className="text-white font-medium">{prediction.eventType}</span>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Condition:</span>
                        <span className="text-white">
                          {getEventConfigText(prediction.eventType, prediction.eventConfig)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Period:</span>
                        <span className="text-white">
                          {formatDate(prediction.startTime)} - {formatDate(prediction.endTime)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Created:</span>
                        <span className="text-white">{formatDate(prediction.createdAt)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </div>

        <div className="p-4 border-t border-gray-700">
          <Button
            onClick={handleCreatePrediction}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            disabled={isLoading}
          >
            <Plus className="h-4 w-4 mr-2" />
            Create New Prediction
          </Button>
        </div>
      </div>

      {/* TODO: Update PredictionDetailModal to work with new architecture */}
      {/* <PredictionDetailModal
        prediction={selectedPrediction}
        onClose={handleCloseModal}
        onBuyTokens={handleBuyTokens}
        isLoading={isLoading}
      /> */}
    </div>
  );
};

export default PredictionDetailsSidebar;

"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { shortenText } from "@/utils/formatting";
import { Droplets, MapPin, Thermometer, Wind, X } from "lucide-react";
import React, { useState } from "react";

import { Prediction } from "../hooks/usePredictions";

interface PredictionDetailModalProps {
  prediction: Prediction | null;
  onClose: () => void;
  onYes: (predictionId: string, amount: string) => void;
  onNo: (predictionId: string, amount: string) => void;
  isLoading?: boolean;
}

const PredictionDetailModal: React.FC<PredictionDetailModalProps> = ({
  prediction,
  onClose,
  onYes,
  onNo,
  isLoading = false,
}) => {
  const [tokenAmount, setTokenAmount] = useState("1");

  if (!prediction) return null;

  const getWeatherIcon = (eventType: string) => {
    switch (eventType) {
      case "rainfall":
        return <Droplets className="h-4 w-4" />;
      case "temperature":
        return <Thermometer className="h-4 w-4" />;
      case "wind":
        return <Wind className="h-4 w-4" />;
      default:
        return <MapPin className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    if (!status) return "bg-gray-900/50 text-gray-400 border-gray-700";

    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-900/50 text-green-400 border-green-700";
      case "completed":
        return "bg-gray-900/50 text-gray-400 border-gray-700";
      case "expired":
        return "bg-red-900/50 text-red-400 border-red-700";
      default:
        return "bg-gray-900/50 text-gray-400 border-gray-700";
    }
  };

  const getEventTypeColor = (eventType: string) => {
    switch (eventType) {
      case "rainfall":
        return "border-blue-500";
      case "temperature":
        return "border-orange-500";
      case "wind":
        return "border-green-500";
      default:
        return "border-gray-700";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getEventConfigText = (eventType: string, eventConfig: any) => {
    if (!eventConfig) return "N/A";

    switch (eventType) {
      case "rainfall":
        return `${eventConfig.operator === "gte" ? "≥" : "≤"} ${eventConfig.threshold}${eventConfig.unit}`;
      case "temperature":
        if (eventConfig.minTemp !== undefined && eventConfig.maxTemp !== undefined) {
          return `${eventConfig.minTemp}°${eventConfig.unit} - ${eventConfig.maxTemp}°${eventConfig.unit}`;
        } else if (eventConfig.threshold !== undefined) {
          return `${eventConfig.threshold}°${eventConfig.unit}`;
        }
        return "N/A";
      case "wind":
        if (eventConfig.maxSpeed !== undefined) {
          return `${eventConfig.operator === "gte" ? "≥" : "≤"} ${eventConfig.maxSpeed} ${eventConfig.unit}`;
        } else if (eventConfig.threshold !== undefined) {
          return `${eventConfig.operator === "gte" ? "≥" : "≤"} ${eventConfig.threshold} ${eventConfig.unit}`;
        }
        return "N/A";
      default:
        return "N/A";
    }
  };

  const handleYes = () => {
    onYes(prediction.id, tokenAmount);
    onClose();
  };

  const handleNo = () => {
    onNo(prediction.id, tokenAmount);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-black/85 backdrop-blur-xl border-2 border-gray-700 rounded-xl p-6 max-w-md w-full mx-4 shadow-xl">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-bold text-white">Prediction Details</h2>
          <Button variant="ghost" size="sm" onClick={onClose} className="h-6 w-6 p-0 hover:bg-gray-700">
            <X className="h-4 w-4" />
          </Button>
        </div>

        <Card className={`bg-black/85 border-2 ${getEventTypeColor(prediction.eventType)} mb-6`}>
          <CardHeader className="pb-2">
            <div className="flex items-center gap-2">
              <Badge className={`${getStatusColor(prediction.status)} text-xs`}>{prediction.status}</Badge>
            </div>
            <CardTitle className="text-sm text-white flex items-center gap-2">
              {getWeatherIcon(prediction.eventType)}
              {prediction.title}
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              <p className="text-xs text-gray-300">{prediction.description}</p>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-400">Type:</span>
                  <span className="text-white capitalize">{prediction.eventType}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Threshold:</span>
                  <span className="text-white">{getEventConfigText(prediction.eventType, prediction.eventConfig)}</span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-400">Start:</span>
                  <span className="text-white">{formatDate(prediction.startTime)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">End:</span>
                  <span className="text-white">{formatDate(prediction.endTime)}</span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-400">Prediction ID:</span>
                  <span className="text-white text-xs font-mono">{shortenText(prediction.id, 20)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Created:</span>
                  <span className="text-white">{formatDate(prediction.createdAt)}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="mb-4">
          <Label htmlFor="tokenAmount" className="text-sm text-gray-300 mb-2 block">
            Token Amount (USDC)
          </Label>
          <Input
            id="tokenAmount"
            type="number"
            value={tokenAmount}
            onChange={(e) => setTokenAmount(e.target.value)}
            min="0.01"
            step="0.01"
            placeholder="1.0"
            className="bg-gray-900 border-gray-600 text-white"
          />
        </div>

        <div className="flex gap-3">
          <Button
            onClick={handleYes}
            disabled={isLoading || !tokenAmount || parseFloat(tokenAmount) <= 0}
            className="flex-1 bg-green-600 hover:bg-green-700 text-white disabled:opacity-50"
          >
            {isLoading ? "Loading..." : "Yes"}
          </Button>
          <Button
            onClick={handleNo}
            disabled={isLoading || !tokenAmount || parseFloat(tokenAmount) <= 0}
            className="flex-1 bg-red-600 hover:bg-red-700 text-white disabled:opacity-50"
          >
            {isLoading ? "Loading..." : "No"}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PredictionDetailModal;

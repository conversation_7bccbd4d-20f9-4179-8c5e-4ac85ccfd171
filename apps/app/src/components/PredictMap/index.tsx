"use client";

import { usePredictionContracts } from "@/hooks/usePredictionContracts";
import { NewBuyTokensParams } from "@/types";
import MapboxDraw from "@mapbox/mapbox-gl-draw";
import React, { useCallback, useState } from "react";

import TradingPanel from "../TradingPanel";
import DrawControls from "./DrawControls";
import DrawableMap from "./DrawableMap";
import PolygonSidebar from "./PolygonSidebar";
import PredictionDetailsSidebar from "./PredictionDetailsSidebar";
import { PolygonFeature, usePolygonDraw } from "./hooks/usePolygonDraw";
import { PolygonFeature as PredictionPolygonFeature, usePredictions } from "./hooks/usePredictions";

interface PredictMapProps {
  className?: string;
}

const PredictMap: React.FC<PredictMapProps> = ({ className }) => {
  const [drawControls, setDrawControls] = useState<MapboxDraw | null>(null);
  const [shouldClearFields, setShouldClearFields] = useState(false);
  const [selectedPolygon, setSelectedPolygon] = useState<PredictionPolygonFeature | null>(null);
  const { polygonFeature, setPolygonFeature, calculateArea, formatTimestamp, clearPolygon, regeneratePolygonId } =
    usePolygonDraw();
  const { refetchPredictions } = usePredictions();
  const { buyTokens, isLoading } = usePredictionContracts();

  const clearAllFormFields = useCallback(() => {
    setShouldClearFields(true);
  }, []);

  const handleFieldsCleared = useCallback(() => {
    setShouldClearFields(false);
  }, []);

  const handlePolygonCreate = useCallback(
    (feature: PolygonFeature) => {
      const area = calculateArea(feature.geometry);
      const updatedFeature: PolygonFeature = {
        ...feature,
        properties: {
          ...feature.properties,
          area_ha: parseFloat(area.toFixed(4)),
          datetime: formatTimestamp(),
        },
      };
      setPolygonFeature(updatedFeature);
    },
    [calculateArea, formatTimestamp, setPolygonFeature],
  );

  const handlePolygonUpdate = useCallback(
    (feature: PolygonFeature) => {
      const area = calculateArea(feature.geometry);
      const updatedFeature: PolygonFeature = {
        ...feature,
        properties: {
          ...feature.properties,
          area_ha: parseFloat(area.toFixed(4)),
        },
      };
      setPolygonFeature(updatedFeature);
    },
    [calculateArea, setPolygonFeature],
  );

  const handlePolygonDelete = useCallback(() => {
    clearPolygon();
    clearAllFormFields();
  }, [clearPolygon, clearAllFormFields]);

  const handleDrawControlsAdd = useCallback((draw: MapboxDraw) => {
    setDrawControls(draw);
  }, []);

  const handleDeleteFromSidebar = useCallback(() => {
    if (drawControls) {
      drawControls.deleteAll();
    }
    clearPolygon();
    clearAllFormFields();
  }, [drawControls, clearPolygon, clearAllFormFields]);

  const handlePolygonClick = useCallback((polygon: PredictionPolygonFeature) => {
    setSelectedPolygon(polygon);
  }, []);

  const handleClosePredictionDetails = useCallback(() => {
    setSelectedPolygon(null);
  }, []);

  const handleCreatePrediction = useCallback(
    (polygonId: string) => {
      setSelectedPolygon(null);

      const tempPolygonFeature: PolygonFeature = {
        id: crypto.randomUUID(),
        geometry: {
          type: "Polygon",
          coordinates: selectedPolygon?.geometry.coordinates || [],
        },
        properties: {
          polygon_id: polygonId,
          area_ha: selectedPolygon?.properties.polygonArea || 0,
          datetime: formatTimestamp(),
        },
      };

      setPolygonFeature(tempPolygonFeature);
    },
    [selectedPolygon, formatTimestamp, setPolygonFeature],
  );

  const handleBuyTokens = useCallback(
    async (predictionAddress: string, outcomeIndex: number, amount: string) => {
      try {
        console.log(
          "User is buying tokens for prediction:",
          predictionAddress,
          "outcome:",
          outcomeIndex,
          "amount:",
          amount,
        );

        const amountInWei = (parseFloat(amount) * 1e18).toString();

        const buyTokensParams: NewBuyTokensParams = {
          predictionAddress,
          outcomeIndex,
          collateralAmount: amountInWei,
        };

        await buyTokens(buyTokensParams);

        refetchPredictions();
      } catch (error) {
        console.error("Error buying tokens:", error);
      }
    },
    [buyTokens, refetchPredictions],
  );

  return (
    <div className={`w-full h-screen relative ${className || ""}`}>
      <DrawableMap
        onPolygonCreate={handlePolygonCreate}
        onPolygonUpdate={handlePolygonUpdate}
        onPolygonDelete={handlePolygonDelete}
        onDrawControlsAdd={handleDrawControlsAdd}
        onPolygonClick={handlePolygonClick}
      />

      <DrawControls draw={drawControls} hasPolygon={!!polygonFeature} onDeletePolygon={handleDeleteFromSidebar} />

      {!selectedPolygon && (
        <PolygonSidebar
          polygonFeature={polygonFeature}
          shouldClearFields={shouldClearFields}
          onFieldsCleared={handleFieldsCleared}
          onRegeneratePolygonId={regeneratePolygonId}
          onPredictionCreated={refetchPredictions}
        />
      )}

      <PredictionDetailsSidebar
        polygon={selectedPolygon}
        onClose={handleClosePredictionDetails}
        onCreatePrediction={handleCreatePrediction}
        onBuyTokens={handleBuyTokens}
        isLoading={isLoading}
      />

      <TradingPanel />

      <div
        className="absolute top-0 left-0 w-full h-full pointer-events-none"
        style={{
          background:
            "linear-gradient(to bottom, rgba(0,0,0,0.9) 0%, rgba(0,0,0,0.1) 15%, rgba(0,0,0,0.1) 85%, rgba(0,0,0,0.9) 100%)",
          zIndex: 10,
        }}
      />
    </div>
  );
};

export default PredictMap;

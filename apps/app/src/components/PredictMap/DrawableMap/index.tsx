"use client";

import { MAPBOX_TOKEN } from "@/utils/constants";
import MapboxDraw from "@mapbox/mapbox-gl-draw";
import "@mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css";
import "mapbox-gl/dist/mapbox-gl.css";
import React, { useCallback, useEffect, useRef } from "react";
import Map, { MapRef } from "react-map-gl";

import { PolygonFeature as DrawPolygonFeature } from "../hooks/usePolygonDraw";
import { PolygonFeature, usePredictions } from "../hooks/usePredictions";

interface DrawableMapProps {
  onPolygonCreate: (feature: DrawPolygonFeature) => void;
  onPolygonUpdate: (feature: DrawPolygonFeature) => void;
  onPolygonDelete: () => void;
  onDrawControlsAdd: (draw: MapboxDraw) => void;
  onPolygonClick: (polygon: PolygonFeature) => void;
}

const DrawableMap: React.FC<DrawableMapProps> = ({
  onPolygonCreate,
  onPolygonUpdate,
  onPolygonDelete,
  onDrawControlsAdd,
  onPolygonClick,
}) => {
  const mapRef = useRef<MapRef>(null);
  const drawRef = useRef<MapboxDraw | null>(null);
  const { polygons } = usePredictions();

  useEffect(() => {
    if (mapRef.current && polygons.length > 0) {
      const map = mapRef.current.getMap();
      const source = map.getSource("predictions");

      if (source && source.type === "geojson") {
        source.setData({
          type: "FeatureCollection",
          features: polygons,
        });
      }
    }
  }, [polygons]);

  const handleMapLoad = useCallback(() => {
    console.log("Map loaded - initializing draw controls");

    if (!mapRef.current) {
      console.error("mapRef.current is null");
      return;
    }

    const map = mapRef.current.getMap();
    console.log("Map instance:", map);

    const draw = new MapboxDraw({
      displayControlsDefault: false,
      defaultMode: "simple_select",
    });

    console.log("Draw instance created:", draw);
    drawRef.current = draw;

    map.addControl(draw, "top-left");
    console.log("Draw control added to map");

    onDrawControlsAdd(draw);
    console.log("Draw controls passed to parent");

    const handleDrawCreate = (e: any) => {
      console.log("Draw create event:", e);
      const feature = e.features[0];
      if (!feature) return;

      const allFeatures = draw.getAll();
      const idsToDelete = allFeatures.features
        .map((f) => f.id)
        .filter((id): id is string => id !== feature.id && id !== undefined);

      if (idsToDelete.length > 0) {
        draw.delete(idsToDelete);
      }

      const polygonFeature: DrawPolygonFeature = {
        id: feature.id,
        geometry: {
          type: "Polygon",
          coordinates: feature.geometry.coordinates,
        },
        properties: {
          polygon_id: crypto.randomUUID(),
          area_ha: 0,
          datetime: "",
        },
      };

      onPolygonCreate(polygonFeature);
    };

    const handleDrawUpdate = (e: any) => {
      console.log("Draw update event:", e);
      const feature = e.features[0];
      if (!feature) return;

      const polygonFeature: DrawPolygonFeature = {
        id: feature.id,
        geometry: {
          type: "Polygon",
          coordinates: feature.geometry.coordinates,
        },
        properties: {
          polygon_id: feature.properties?.polygon_id || crypto.randomUUID(),
          area_ha: 0,
          datetime: feature.properties?.datetime || "",
        },
      };

      onPolygonUpdate(polygonFeature);
    };

    const handleDrawDelete = () => {
      console.log("Draw delete event");
      onPolygonDelete();
    };

    const setupPredictionsLayers = () => {
      map.addSource("predictions", {
        type: "geojson",
        data: {
          type: "FeatureCollection",
          features: [],
        },
      });

      // Use a default color for all polygons since we no longer differentiate by eventType
      map.addLayer({
        id: "predictions-fill",
        type: "fill",
        source: "predictions",
        layout: {
          "fill-sort-key": ["-", ["get", "polygonArea"]],
        },
        paint: {
          "fill-color": "#3b82f6",
          "fill-opacity": 0.3,
        },
      });

      map.addLayer({
        id: "predictions-outline",
        type: "line",
        source: "predictions",
        layout: {
          "line-sort-key": ["-", ["get", "polygonArea"]],
        },
        paint: {
          "line-color": "#3b82f6",
          "line-width": 2,
          "line-opacity": 0.8,
        },
      });

      map.addLayer({
        id: "predictions-fill-hover",
        type: "fill",
        source: "predictions",
        layout: {
          "fill-sort-key": ["-", ["get", "polygonArea"]],
        },
        paint: {
          "fill-color": "#1d4ed8",
          "fill-opacity": 0.6,
        },
        filter: ["==", ["get", "polygonId"], ""],
      });

      map.addLayer({
        id: "predictions-outline-hover",
        type: "line",
        source: "predictions",
        layout: {
          "line-sort-key": ["-", ["get", "polygonArea"]],
        },
        paint: {
          "line-color": "#1d4ed8",
          "line-width": 2,
          "line-opacity": 1,
        },
        filter: ["==", ["get", "polygonId"], ""],
      });
    };

    const handlePolygonClick = (e: any) => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: ["predictions-fill"],
      });

      if (features.length > 0) {
        const feature = features[0];
        const polygon: PolygonFeature = {
          type: "Feature",
          geometry: feature.geometry as any,
          properties: feature.properties as any,
        };
        onPolygonClick(polygon);
      }
    };

    setupPredictionsLayers();

    map.on("draw.create", handleDrawCreate);
    map.on("draw.update", handleDrawUpdate);
    map.on("draw.delete", handleDrawDelete);
    map.on("click", "predictions-fill", handlePolygonClick);

    let currentHoveredId: string | null = null;

    const handleMouseMove = (e: any) => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: ["predictions-fill"],
      });

      if (features.length > 0) {
        const hoveredFeature = features[0];
        const featureId = hoveredFeature.properties?.polygonId;

        if (featureId && featureId !== currentHoveredId) {
          if (currentHoveredId !== null) {
            map.setFilter("predictions-fill-hover", ["==", ["get", "polygonId"], ""]);
            map.setFilter("predictions-outline-hover", ["==", ["get", "polygonId"], ""]);
          }

          currentHoveredId = featureId;
          map.getCanvas().style.cursor = "pointer";
          map.setFilter("predictions-fill-hover", ["==", ["get", "polygonId"], featureId]);
          map.setFilter("predictions-outline-hover", ["==", ["get", "polygonId"], featureId]);
        }
      } else {
        if (currentHoveredId !== null) {
          currentHoveredId = null;
          map.getCanvas().style.cursor = "";
          map.setFilter("predictions-fill-hover", ["==", ["get", "polygonId"], ""]);
          map.setFilter("predictions-outline-hover", ["==", ["get", "polygonId"], ""]);
        }
      }
    };

    const handleMouseLeave = () => {
      if (currentHoveredId !== null) {
        currentHoveredId = null;
        map.getCanvas().style.cursor = "";
        map.setFilter("predictions-fill-hover", ["==", ["get", "polygonId"], ""]);
        map.setFilter("predictions-outline-hover", ["==", ["get", "polygonId"], ""]);
      }
    };

    map.on("mousemove", "predictions-fill", handleMouseMove);
    map.on("mouseleave", "predictions-fill", handleMouseLeave);

    const handleGeneralMouseMove = (e: any) => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: ["predictions-fill"],
      });

      if (features.length > 0) {
        map.getCanvas().style.cursor = "pointer";
      } else {
        map.getCanvas().style.cursor = "";
      }
    };

    map.on("mousemove", handleGeneralMouseMove);

    return () => {
      console.log("Cleaning up draw controls");
      map.off("draw.create", handleDrawCreate);
      map.off("draw.update", handleDrawUpdate);
      map.off("draw.delete", handleDrawDelete);
      map.off("click", "predictions-fill", handlePolygonClick);
      map.off("mousemove", "predictions-fill", handleMouseMove);
      map.off("mouseleave", "predictions-fill", handleMouseLeave);
      map.off("mousemove", handleGeneralMouseMove);

      if (drawRef.current) {
        map.removeControl(drawRef.current);
        drawRef.current = null;
      }

      if (map.getLayer("predictions-outline-hover")) {
        map.removeLayer("predictions-outline-hover");
      }
      if (map.getLayer("predictions-fill-hover")) {
        map.removeLayer("predictions-fill-hover");
      }
      if (map.getLayer("predictions-outline")) {
        map.removeLayer("predictions-outline");
      }
      if (map.getLayer("predictions-fill")) {
        map.removeLayer("predictions-fill");
      }
      if (map.getSource("predictions")) {
        map.removeSource("predictions");
      }
    };
  }, [onPolygonCreate, onPolygonUpdate, onPolygonDelete, onDrawControlsAdd, onPolygonClick]);

  return (
    <div className="w-full h-full">
      <Map
        ref={mapRef}
        mapboxAccessToken={MAPBOX_TOKEN}
        onLoad={handleMapLoad}
        initialViewState={{
          longitude: -46.6333,
          latitude: -23.5505,
          zoom: 11,
        }}
        style={{ width: "100%", height: "100%" }}
        mapStyle="mapbox://styles/mapbox/satellite-streets-v12"
        attributionControl={false}
      />
    </div>
  );
};

export default DrawableMap;

'use client'

import { cn } from "@/lib/utils";
import { useEffect, useRef, useState, forwardRef } from "react";
import gsap from "gsap";

import Badge1 from "@/static/svg/badge1.svg";
import Badge2 from "@/static/svg/badge2.svg";
import Badge3 from "@/static/svg/badge3.svg";
import Badge4 from "@/static/svg/badge4.svg";
import Badge5 from "@/static/svg/badge5.svg";
import Badge6 from "@/static/svg/badge6.svg";
import Badge7 from "@/static/svg/badge7.svg";
import Badge8 from "@/static/svg/badge8.svg";
import Badge9 from "@/static/svg/badge9.svg";
import Badge10 from "@/static/svg/badge10.svg";
import DelimiterSvg from "@/static/svg/delimiter.svg";

const DelimiterIcon = DelimiterSvg;

const Frames = ({ direction = 'horizontal' }: { direction?: 'horizontal' | 'vertical' }) => {
  return (
    <div
      className={cn(
        "fixed inset-0 flex justify-between items-center pointer-events-none z-[99] w-full h-full p-8",
        direction === 'vertical' ? "flex-col" : "flex-row"
      )}
    >
      <div className={cn(
        "inline-grid w-[38px] h-2 opacity-30",
        direction === 'vertical' ? "" : "transform -translate-x-2.5 -rotate-90"
      )}>
        <DelimiterIcon />
      </div>
      <div className={cn(
        "inline-grid w-[38px] h-2 opacity-30",
        direction === 'vertical' ? "rotate-180" : "transform translate-x-2.5 rotate-90"
      )}>
        <DelimiterIcon />
      </div>
    </div>
  );
};

const Badges = forwardRef<HTMLDivElement, { className?: string }>(({ className }, ref) => {
  return (
    <div className={className} ref={ref}>
      <style>
        {`
          .loading-badge {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0;
            transform: scale(0.8);
            animation: loading-badge-show 1000ms infinite ease-in-out;
          }
          @keyframes loading-badge-show {
            0%, 9.99% { opacity: 0; transform: scale(0.8); }
            10%, 19.99% { opacity: 1; transform: scale(1); }
            20%, 100% { opacity: 0; transform: scale(0.8); }
          }
          .badge1 { animation-delay: 0ms; }
          .badge2 { animation-delay: 100ms; }
          .badge3 { animation-delay: 200ms; }
          .badge4 { animation-delay: 300ms; }
          .badge5 { animation-delay: 400ms; }
          .badge6 { animation-delay: 500ms; }
          .badge7 { animation-delay: 600ms; }
          .badge8 { animation-delay: 700ms; }
          .badge9 { animation-delay: 800ms; }
          .badge10 { animation-delay: 900ms; }
        `}
      </style>
      <Badge1 className="loading-badge badge1 text-secondary-dark" />
      <Badge2 className="loading-badge badge2 text-secondary-dark" />
      <Badge3 className="loading-badge badge3 text-secondary-dark" />
      <Badge4 className="loading-badge badge4 text-secondary-dark" />
      <Badge5 className="loading-badge badge5 text-secondary-dark" />
      <Badge6 className="loading-badge badge6 text-secondary-dark" />
      <Badge7 className="loading-badge badge7 text-secondary-dark" />
      <Badge8 className="loading-badge badge8 text-secondary-dark" />
      <Badge9 className="loading-badge badge9 text-secondary-dark" />
      <Badge10 className="loading-badge badge10 text-secondary-dark" />
    </div>
  );
});

Badges.displayName = 'Badges';

interface LoadingStateProps {
  text?: string;
  className?: string;
  variant?: "fullscreen" | "inline";
  onComplete?: () => void;
}

export default function LoadingState({
  text = "Loading...",
  className,
  variant = "fullscreen",
  onComplete,
}: LoadingStateProps) {
  const progressRef = useRef<HTMLDivElement>(null);
  const barRef = useRef<HTMLDivElement>(null);
  const badgesRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<any>();
  const [timeline, setTimeline] = useState<any>();
  const [pointer, setPointer] = useState(false);
  const [assetsLoaded, setAssetsLoaded] = useState(false);
  const [progress, setProgress] = useState(0);
  const [total, setTotal] = useState(60);

  // Simulate fast loading progress
  useEffect(() => {
    const interval = setInterval(() => {
      setProgress((prev) => {
        const increment = Math.random() * 15 + 5; // Random increment between 5-20
        const newProgress = Math.min(prev + increment, total);
        return newProgress;
      });
    }, 50); // Fast progress updates

    return () => clearInterval(interval);
  }, [total]);

  useEffect(() => {
    const tl: any = gsap.timeline({ paused: true });

    tl.to(badgesRef.current, {
      opacity: 0,
      duration: 1,
      onUpdate: () => {
        if (progressRef.current) {
          // Durante a animação final, --progress-out diminui de 1 para 0
          // criando o efeito de duas linhas que se afastam do centro
          progressRef.current.style.setProperty(
            '--progress-out',
            String(1 - (tl?.progress() || 0))
          );
        }
      },
      onComplete: () => {
        setAssetsLoaded(true);
        timeoutRef.current = setTimeout(() => {
          setPointer(true);
          if (badgesRef.current) {
            badgesRef.current.classList.add('remove');
          }
          onComplete?.();
        }, 100);
      },
    });

    setTimeline(tl);

    return () => {
      tl.kill();
    };
  }, [onComplete]);

  useEffect(() => {
    if (timeline?.isActive()) {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    }
  }, [timeline]);

  useEffect(() => {
    if (progressRef.current) {
      const progressInValue = Math.min(progress / total, 1);
      progressRef.current.style.setProperty(
        '--progress-in',
        String(progressInValue)
      );

      progressRef.current.style.setProperty(
        '--progress-out',
        '1'
      );
    }

    if (progress >= total && !timeline?.isActive() && barRef.current) {
      barRef.current.classList.add('animation-progress-in');
    }
  }, [progress, timeline, total]);

  if (variant === "inline") {
    return (
      <div className={cn("flex items-center gap-3 bg-black/50 p-3 rounded-lg", className)}>
        <div className="relative w-10 h-10">
          <Badges className="relative w-full h-full" />
        </div>
        <p className="text-base text-gray-400 font-semibold" style={{ fontFamily: 'Denim, monospace' }}>{text}</p>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "fixed inset-0 z-[100] w-full h-dvh bg-black flex flex-col justify-center items-center",
        assetsLoaded && "opacity-0 pointer-events-none transition-opacity duration-1000",
        pointer && "pointer-events-none",
        className,
      )}
      style={{
        fontFamily: "'Denim', 'system-ui', 'monospace', sans-serif"
      }}
    >
      <Frames direction="horizontal" />
      <Frames direction="vertical" />

      <style>
        {`
          .animation-progress-in {
            animation: progress-in 1s ease-out forwards;
          }
          
          @keyframes progress-in {
            from {
              transform: scaleX(0);
            }
            to {
              transform: scaleX(1);
            }
          }
          
          .remove {
            animation: fadeOut 0.5s ease-out forwards;
          }
          
          @keyframes fadeOut {
            to {
              opacity: 0;
              transform: scale(0.8);
            }
          }
        `}
      </style>

      <Badges
        className={cn(
          "relative w-16 h-16 mb-8",
          assetsLoaded && "remove"
        )}
        ref={badgesRef}
      />

      <div
        className="relative rounded overflow-hidden mt-6"
        ref={progressRef}
        style={{
          '--progress-in': '0',
          '--progress-out': '1',
          transform: `scaleX(var(--progress-out))`,
          transformOrigin: 'center',
          height: '1px',
          width: '160px',
          backgroundColor: '#4A4A4A'
        } as React.CSSProperties}
      >
        <div
          ref={barRef}
          className="absolute inset-0 h-full origin-center"
          style={{
            transform: `scaleX(var(--progress-in))`,
            zIndex: 1,
            backgroundColor: '#ACFF46',
            height: '1px'
          }}
          onAnimationEnd={(e) => {
            if (e.animationName === 'progress-in') {
              timeline?.play();
            }
          }}
        />
      </div>

      <p className="text-sm text-gray-500 font-medium mt-6" style={{ fontFamily: 'Denim, monospace' }}>{text}</p>
    </div>
  );
}

"use client";

import {
  // CreatePredictionInput,
  NewBuyTokensParams,
  NewClaimRewardsParams,
  NewCreatePredictionParams,
  WeatherCondition,
} from "@/types";
import {
  ERC20_ABI,
  FACTORY_ABI,
  PREDICTION_ABI,
  SMART_CONTRACT_CONFIG, // ZEUS_TOKEN_ABI,
  // ZEUS_TOKEN_ABI
} from "@/utils/contracts";
import { shortenText } from "@/utils/formatting";
import { useSendTransaction, useWallets } from "@privy-io/react-auth";
import { useCallback, useState } from "react";
import { encodeFunctionData, formatUnits, parseEther } from "viem";
import { useWriteContract } from "wagmi";

import { useToast } from "./useToast";

export function usePredictionContracts() {
  const { wallets } = useWallets();
  const { sendTransaction: privySendTransaction } = useSendTransaction();
  const { writeContractAsync: wagmiWriteContract } = useWriteContract();
  const { toast } = useToast();

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const isEmbeddedWallet = !!wallets?.find((wallet) => (wallet as any)?.walletClientType === "privy");

  const weatherConditionToString = useCallback((condition: WeatherCondition): string => {
    switch (condition.type) {
      case "rainfall":
        const rainfallData = condition.data as any;
        const operator = rainfallData.operator === "gte" ? ">=" : "<=";
        return `${operator} ${rainfallData.threshold}${rainfallData.unit} rainfall`;
      case "temperature":
        const tempData = condition.data as any;
        const unit = tempData.unit === "celsius" ? "°C" : "°F";
        return `${tempData.minTemp}${unit} to ${tempData.maxTemp}${unit}`;
      case "wind":
        const windData = condition.data as any;
        const windOperator = windData.operator === "gte" ? ">=" : "<=";
        return `${windOperator} ${windData.maxSpeed}${windData.unit} wind speed`;
      default:
        return JSON.stringify(condition);
    }
  }, []);

  const createPredictionWithWagmi = useCallback(
    async (params: NewCreatePredictionParams) => {
      await wagmiWriteContract({
        address: SMART_CONTRACT_CONFIG.collateralTokenAddress as `0x${string}`,
        abi: ERC20_ABI,
        functionName: "approve",
        args: [SMART_CONTRACT_CONFIG.factoryAddress as `0x${string}`, BigInt(params.initialLiquidity)],
      });

      const hash = await wagmiWriteContract({
        address: SMART_CONTRACT_CONFIG.factoryAddress as `0x${string}`,
        abi: FACTORY_ABI,
        functionName: "createPrediction",
        args: [params.input as any, params.signature as `0x${string}`, BigInt(params.initialLiquidity)],
      });

      return hash;
    },
    [wagmiWriteContract],
  );

  const createPredictionWithPrivy = useCallback(
    async (params: NewCreatePredictionParams) => {
      const approveData = encodeFunctionData({
        abi: ERC20_ABI,
        functionName: "approve",
        args: [SMART_CONTRACT_CONFIG.factoryAddress as `0x${string}`, BigInt(params.initialLiquidity)],
      });

      await privySendTransaction({
        to: SMART_CONTRACT_CONFIG.collateralTokenAddress as `0x${string}`,
        data: approveData,
        value: "0x0",
        gasLimit: 100000,
      });

      const createPredictionData = encodeFunctionData({
        abi: FACTORY_ABI,
        functionName: "createPrediction",
        args: [params.input as any, params.signature as `0x${string}`, BigInt(params.initialLiquidity)],
      });

      const createTx = await privySendTransaction({
        to: SMART_CONTRACT_CONFIG.factoryAddress as `0x${string}`,
        data: createPredictionData,
        value: "0x0",
        gasLimit: 1000000,
      });

      return (createTx as any).hash || createTx.transactionHash;
    },
    [privySendTransaction],
  );

  const createPrediction = useCallback(
    async (params: NewCreatePredictionParams) => {
      try {
        setIsLoading(true);
        setError(null);

        let hash: string;

        if (!isEmbeddedWallet) {
          hash = await createPredictionWithWagmi(params);
        } else {
          hash = await createPredictionWithPrivy(params);
        }

        toast({
          title: "Prediction created successfully!",
          description: `Transaction hash: ${hash}`,
          duration: 5000,
        });

        return hash;
      } catch (err) {
        console.error("Error in createPrediction:", err);
        const errorMessage = err instanceof Error ? err.message : "Failed to create prediction";
        setError(errorMessage);

        toast({
          title: "Error creating prediction",
          description: errorMessage,
          variant: "destructive",
          duration: 5000,
        });

        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [createPredictionWithWagmi, createPredictionWithPrivy, isEmbeddedWallet, toast],
  );

  const buyTokensWithWagmi = useCallback(
    async (params: NewBuyTokensParams) => {
      await wagmiWriteContract({
        address: SMART_CONTRACT_CONFIG.collateralTokenAddress as `0x${string}`,
        abi: ERC20_ABI,
        functionName: "approve",
        args: [params.predictionAddress as `0x${string}`, BigInt(params.collateralAmount)],
      });

      const hash = await wagmiWriteContract({
        address: params.predictionAddress as `0x${string}`,
        abi: PREDICTION_ABI,
        functionName: "buyTokens",
        args: [BigInt(params.outcomeIndex), BigInt(params.collateralAmount)],
      });

      return hash;
    },
    [wagmiWriteContract],
  );

  const buyTokensWithPrivy = useCallback(
    async (params: NewBuyTokensParams) => {
      const approveData = encodeFunctionData({
        abi: ERC20_ABI,
        functionName: "approve",
        args: [params.predictionAddress as `0x${string}`, BigInt(params.collateralAmount)],
      });

      await privySendTransaction({
        to: SMART_CONTRACT_CONFIG.collateralTokenAddress as `0x${string}`,
        data: approveData,
        value: "0x0",
        gasLimit: 100000,
      });

      const buyData = encodeFunctionData({
        abi: PREDICTION_ABI,
        functionName: "buyTokens",
        args: [BigInt(params.outcomeIndex), BigInt(params.collateralAmount)],
      });

      const buyTx = await privySendTransaction({
        to: params.predictionAddress as `0x${string}`,
        data: buyData,
        value: "0x0",
        gasLimit: 1000000,
      });

      return (buyTx as any).hash || buyTx.transactionHash;
    },
    [privySendTransaction],
  );

  const buyTokens = useCallback(
    async (params: NewBuyTokensParams) => {
      try {
        setIsLoading(true);
        setError(null);

        let hash: string;

        if (!isEmbeddedWallet) {
          hash = await buyTokensWithWagmi(params);
        } else {
          hash = await buyTokensWithPrivy(params);
        }

        toast({
          title: "Tokens bought successfully!",
          description: `Transaction hash: ${shortenText(hash, 10)}`,
          duration: 5000,
        });

        return hash;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to buy tokens";
        setError(errorMessage);

        toast({
          title: "Error buying tokens",
          description: errorMessage,
          variant: "destructive",
          duration: 5000,
        });

        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [buyTokensWithWagmi, buyTokensWithPrivy, isEmbeddedWallet, toast],
  );

  const claimRewardsWithWagmi = useCallback(
    async (params: NewClaimRewardsParams) => {
      const hash = await wagmiWriteContract({
        address: params.predictionAddress as `0x${string}`,
        abi: PREDICTION_ABI,
        functionName: "claimRewards",
        args: [BigInt(params.outcomeIndex)],
      });

      return hash;
    },
    [wagmiWriteContract],
  );

  const claimRewardsWithPrivy = useCallback(
    async (params: NewClaimRewardsParams) => {
      const claimData = encodeFunctionData({
        abi: PREDICTION_ABI,
        functionName: "claimRewards",
        args: [BigInt(params.outcomeIndex)],
      });

      const claimTx = await privySendTransaction({
        to: params.predictionAddress as `0x${string}`,
        data: claimData,
        value: "0x0",
        gasLimit: 500000,
      });

      return (claimTx as any).hash || claimTx.transactionHash;
    },
    [privySendTransaction],
  );

  const claimRewards = useCallback(
    async (params: NewClaimRewardsParams) => {
      try {
        setIsLoading(true);
        setError(null);

        let hash: string;

        if (!isEmbeddedWallet) {
          hash = await claimRewardsWithWagmi(params);
        } else {
          hash = await claimRewardsWithPrivy(params);
        }

        toast({
          title: "Rewards claimed successfully!",
          description: `Transaction hash: ${shortenText(hash, 10)}`,
          duration: 5000,
        });

        return hash;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to claim rewards";
        setError(errorMessage);

        toast({
          title: "Error claiming rewards",
          description: errorMessage,
          variant: "destructive",
          duration: 5000,
        });

        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [claimRewardsWithWagmi, claimRewardsWithPrivy, isEmbeddedWallet, toast],
  );

  const weiToEther = useCallback((wei: string) => {
    return formatUnits(BigInt(wei), 18);
  }, []);

  const etherToWei = useCallback((ether: string) => {
    return parseEther(ether).toString();
  }, []);

  return {
    // Actions
    createPrediction,
    buyTokens,
    claimRewards,

    // Helpers
    weiToEther,
    etherToWei,
    weatherConditionToString,

    // State
    isLoading,
    error,

    // Wallet info
    walletType: isEmbeddedWallet ? "embedded" : "wallet",
    isWalletConnected: isEmbeddedWallet,

    // Config
    contractConfig: SMART_CONTRACT_CONFIG,
  };
}

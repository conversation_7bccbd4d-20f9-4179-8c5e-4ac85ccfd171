import { useToast } from "@/hooks/useToast";
import { useValidateCodeMutation } from "@/queries/mutations/auth.mutations";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { authService } from "@/services/auth.service";

import usePrivyAuth from "./usePrivyAuth";

const useAuthentication = () => {
  const searchParams = useSearchParams();
  const { ready, authenticated, confirmLogout } = usePrivyAuth();
  const { toast } = useToast();
  const [isValidating, setIsValidating] = useState(true);
  const inviteCode = searchParams?.get("inviteCode");

  const validateCodeMutation = useValidateCodeMutation({
    onSuccess: (data) => {
      toast({
        title: "Success",
        description: data.message || "Invite code validated successfully",
      });
      authService.setSession("LOGGED_IN");
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to validate invite code",
      });
      confirmLogout(true);
    },
  });

  const handleValidations = async () => {
    if (!isValidating) return;

    if (inviteCode) {
      await validateCodeMutation.mutateAsync(inviteCode);
    }

    setIsValidating(false);
    authService.setSession("LOGGED_IN");
  };

  useEffect(() => {
    if (authenticated) {
      handleValidations();
    }
  }, [authenticated, inviteCode]);

  useEffect(() => {
    if (ready && !authenticated) {
      confirmLogout(true);
    }
  }, [ready, authenticated]);

  return { authenticated, ready, isValidating };
};

export default useAuthentication;

import { useToast } from "@/hooks/useToast";
import { useValidateCodeMutation } from "@/queries/mutations/auth.mutations";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { authService } from "@/services/auth.service";

import useBackendValidation from "./useBackendValidation";
import usePrivyAuth from "./usePrivyAuth";

const useAuthentication = () => {
  const searchParams = useSearchParams();
  const { ready, authenticated, confirmLogout } = usePrivyAuth();
  const { toast } = useToast();

  // Authentication states
  const [isValidatingCode, setIsValidatingCode] = useState(false);
  const [isFullyAuthenticated, setIsFullyAuthenticated] = useState(false);

  // Get backend validation hook
  const {
    isValidating: isValidatingBackend,
    hasBackendAccess,
    validateAccess
  } = useBackendValidation();

  const inviteCode = searchParams?.get("inviteCode");

  const validateCodeMutation = useValidateCodeMutation({
    onSuccess: (data) => {
      toast({
        title: "Success",
        description: data.message || "Invite code validated successfully",
      });
      authService.setSession("LOGGED_IN");

      // After invite code validation, validate backend access
      validateAccess();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to validate invite code",
      });
      confirmLogout(true);
    },
  });

  const handleValidations = async () => {
    if (isValidatingCode) return;
    setIsValidatingCode(true);

    try {
      if (inviteCode) {
        // If invite code exists, validate it first
        await validateCodeMutation.mutateAsync(inviteCode);
      } else {
        // No invite code, directly validate backend access
        authService.setSession("LOGGED_IN");
        await validateAccess();
      }
    } catch (error) {
      console.error("Validation error:", error);
    } finally {
      setIsValidatingCode(false);
    }
  };

  // Effect to start validation when user is authenticated with Privy
  useEffect(() => {
    if (authenticated && !isFullyAuthenticated) {
      handleValidations();
    }
  }, [authenticated, inviteCode]);

  // Effect to handle backend validation result
  useEffect(() => {
    if (!isValidatingBackend && hasBackendAccess !== null) {
      if (hasBackendAccess) {
        setIsFullyAuthenticated(true);
        // User has backend access, can proceed to app
      } else {
        // User doesn't have backend access, redirect to landing
        authService.setSession("UNAUTHORIZED_LOGIN_ATTEMPT");
        confirmLogout(true);
      }
    }
  }, [isValidatingBackend, hasBackendAccess]);

  // Effect to handle unauthenticated state
  useEffect(() => {
    if (ready && !authenticated) {
      confirmLogout(true);
    }
  }, [ready, authenticated]);

  return {
    authenticated,
    ready,
    isValidating: isValidatingCode || isValidatingBackend,
    isFullyAuthenticated
  };
};

export default useAuthentication;

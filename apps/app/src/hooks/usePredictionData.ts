"use client";

import { FullPredictionInfo, PredictionMetadata, TokenInfo } from "@/types";
import { SMART_CONTRACT_CONFIG, VIEW_ABI, ZEUS_TOKEN_ABI } from "@/utils/contracts";
import { useCallback, useState } from "react";
import { usePublicClient } from "wagmi";

export function usePredictionData() {
  const publicClient = usePublicClient();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getFullPredictionInfo = useCallback(
    async (predictionAddress: string): Promise<FullPredictionInfo | null> => {
      if (!publicClient) return null;

      try {
        setIsLoading(true);
        setError(null);

        const result = await publicClient.readContract({
          address: SMART_CONTRACT_CONFIG.viewAddress as `0x${string}`,
          abi: VIEW_ABI,
          functionName: "getFullPredictionInfo",
          args: [predictionAddress as `0x${string}`],
        });

        return result as unknown as FullPredictionInfo;
      } catch (err) {
        console.error("Error fetching prediction info:", err);
        const errorMessage = err instanceof Error ? err.message : "Failed to fetch prediction info";
        setError(errorMessage);
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [publicClient],
  );

  const getPredictions = useCallback(
    async (offset: number = 0, limit: number = 10): Promise<PredictionMetadata[]> => {
      if (!publicClient) return [];

      try {
        setIsLoading(true);
        setError(null);

        const result = await publicClient.readContract({
          address: SMART_CONTRACT_CONFIG.viewAddress as `0x${string}`,
          abi: VIEW_ABI,
          functionName: "getPredictions",
          args: [BigInt(offset), BigInt(limit)],
        });

        return result as unknown as PredictionMetadata[];
      } catch (err) {
        console.error("Error fetching predictions:", err);
        const errorMessage = err instanceof Error ? err.message : "Failed to fetch predictions";
        setError(errorMessage);
        return [];
      } finally {
        setIsLoading(false);
      }
    },
    [publicClient],
  );

  const getPredictionsByPolygon = useCallback(
    async (polygonHash: string): Promise<PredictionMetadata[]> => {
      if (!publicClient) return [];

      try {
        setIsLoading(true);
        setError(null);

        const result = await publicClient.readContract({
          address: SMART_CONTRACT_CONFIG.viewAddress as `0x${string}`,
          abi: VIEW_ABI,
          functionName: "getPredictionsByPolygon",
          args: [polygonHash as `0x${string}`],
        });

        return result as unknown as PredictionMetadata[];
      } catch (err) {
        console.error("Error fetching predictions by polygon:", err);
        const errorMessage = err instanceof Error ? err.message : "Failed to fetch predictions by polygon";
        setError(errorMessage);
        return [];
      } finally {
        setIsLoading(false);
      }
    },
    [publicClient],
  );

  const getTotalPredictions = useCallback(async (): Promise<number> => {
    if (!publicClient) return 0;

    try {
      setIsLoading(true);
      setError(null);

      const result = await publicClient.readContract({
        address: SMART_CONTRACT_CONFIG.viewAddress as `0x${string}`,
        abi: VIEW_ABI,
        functionName: "getTotalPredictions",
        args: [],
      });

      return Number(result);
    } catch (err) {
      console.error("Error fetching total predictions:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch total predictions";
      setError(errorMessage);
      return 0;
    } finally {
      setIsLoading(false);
    }
  }, [publicClient]);

  const getTokenBalance = useCallback(
    async (userAddress: string, predictionAddress: string, outcomeIndex: number): Promise<string> => {
      if (!publicClient) return "0";

      try {
        setIsLoading(true);
        setError(null);

        // Get token ID from prediction address and outcome index
        const tokenId = await publicClient.readContract({
          address: SMART_CONTRACT_CONFIG.tokenAddress as `0x${string}`,
          abi: ZEUS_TOKEN_ABI,
          functionName: "getTokenId",
          args: [predictionAddress as `0x${string}`, BigInt(outcomeIndex)],
        });

        // Get balance for the token ID
        const balance = await publicClient.readContract({
          address: SMART_CONTRACT_CONFIG.tokenAddress as `0x${string}`,
          abi: ZEUS_TOKEN_ABI,
          functionName: "balanceOf",
          args: [userAddress as `0x${string}`, tokenId],
        });

        return balance.toString();
      } catch (err) {
        console.error("Error fetching token balance:", err);
        const errorMessage = err instanceof Error ? err.message : "Failed to fetch token balance";
        setError(errorMessage);
        return "0";
      } finally {
        setIsLoading(false);
      }
    },
    [publicClient],
  );

  const getTotalSupply = useCallback(
    async (predictionAddress: string, outcomeIndex: number): Promise<string> => {
      if (!publicClient) return "0";

      try {
        setIsLoading(true);
        setError(null);

        const totalSupply = await publicClient.readContract({
          address: SMART_CONTRACT_CONFIG.tokenAddress as `0x${string}`,
          abi: ZEUS_TOKEN_ABI,
          functionName: "getTotalSupply",
          args: [predictionAddress as `0x${string}`, BigInt(outcomeIndex)],
        });

        return totalSupply.toString();
      } catch (err) {
        console.error("Error fetching total supply:", err);
        const errorMessage = err instanceof Error ? err.message : "Failed to fetch total supply";
        setError(errorMessage);
        return "0";
      } finally {
        setIsLoading(false);
      }
    },
    [publicClient],
  );

  const getTokenInfo = useCallback(
    async (predictionAddress: string, outcomeIndex: number, userAddress?: string): Promise<TokenInfo> => {
      if (!publicClient) return { tokenId: "0", balance: "0", totalSupply: "0" };

      try {
        setIsLoading(true);
        setError(null);

        // Get token ID
        const tokenId = await publicClient.readContract({
          address: SMART_CONTRACT_CONFIG.tokenAddress as `0x${string}`,
          abi: ZEUS_TOKEN_ABI,
          functionName: "getTokenId",
          args: [predictionAddress as `0x${string}`, BigInt(outcomeIndex)],
        });

        const totalSupply = await publicClient.readContract({
          address: SMART_CONTRACT_CONFIG.tokenAddress as `0x${string}`,
          abi: ZEUS_TOKEN_ABI,
          functionName: "getTotalSupply",
          args: [predictionAddress as `0x${string}`, BigInt(outcomeIndex)],
        });

        let balance = "0";
        if (userAddress) {
          balance = (
            await publicClient.readContract({
              address: SMART_CONTRACT_CONFIG.tokenAddress as `0x${string}`,
              abi: ZEUS_TOKEN_ABI,
              functionName: "balanceOf",
              args: [userAddress as `0x${string}`, tokenId],
            })
          ).toString();
        }

        return {
          tokenId: tokenId.toString(),
          balance: balance.toString(),
          totalSupply: totalSupply.toString(),
        };
      } catch (err) {
        console.error("Error fetching token info:", err);
        const errorMessage = err instanceof Error ? err.message : "Failed to fetch token info";
        setError(errorMessage);
        return { tokenId: "0", balance: "0", totalSupply: "0" };
      } finally {
        setIsLoading(false);
      }
    },
    [publicClient],
  );

  return {
    // Prediction queries
    getFullPredictionInfo,
    getPredictions,
    getPredictionsByPolygon,
    getTotalPredictions,

    // Token queries
    getTokenBalance,
    getTotalSupply,
    getTokenInfo,

    // State
    isLoading,
    error,
  };
}

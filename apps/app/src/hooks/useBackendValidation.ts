import { authService } from "@/services/auth.service";
import { useState } from "react";

interface UseBackendValidationReturn {
  isValidating: boolean;
  hasBackendAccess: boolean | null;
  error: Error | null;
  validateAccess: () => Promise<void>;
}

/**
 * Hook to validate if user has access to backend by checking if they can retrieve their referral code
 * Logic: If user can get their own referral code, they have full access to the app
 */
const useBackendValidation = (): UseBackendValidationReturn => {
  const [isValidating, setIsValidating] = useState(false);
  const [hasBackendAccess, setHasBackendAccess] = useState<boolean | null>(null);
  const [error, setError] = useState<Error | null>(null);

  const validateAccess = async () => {
    if (isValidating) return;

    setIsValidating(true);
    setError(null);

    try {
      // Try to get user's referral code - if successful, user has backend access
      await authService.getReferralCode();
      setHasBackendAccess(true);
    } catch (err) {
      console.log("Backend validation failed:", err);
      setHasBackendAccess(false);
      setError(err instanceof Error ? err : new Error("Backend validation failed"));
    } finally {
      setIsValidating(false);
    }
  };

  return {
    isValidating,
    hasBackendAccess,
    error,
    validateAccess,
  };
};

export default useBackendValidation;

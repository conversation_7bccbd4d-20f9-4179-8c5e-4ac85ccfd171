import { SmartContractConfig } from "@/types";

// ABI for Factory contract - manages creation of predictions
export const FACTORY_ABI = [
  {
    type: "constructor",
    inputs: [
      { name: "_collateralToken", type: "address", internalType: "address" },
      { name: "_rebalancer", type: "address", internalType: "address" },
      { name: "_template", type: "address", internalType: "address" },
      { name: "_token", type: "address", internalType: "address" },
    ],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "createPrediction",
    inputs: [
      {
        name: "_input",
        type: "tuple",
        internalType: "struct IPrediction.Metadata",
        components: [
          { name: "polygonId", type: "string", internalType: "string" },
          { name: "polygonCid", type: "string", internalType: "string" },
          { name: "conditions", type: "string[]", internalType: "string[]" },
          { name: "outcomeSlot", type: "uint256", internalType: "uint256" },
          { name: "startDate", type: "uint256", internalType: "uint256" },
          { name: "endDate", type: "uint256", internalType: "uint256" },
          { name: "resolvers", type: "address[]", internalType: "address[]" },
          { name: "resolved", type: "bool", internalType: "bool" },
        ],
      },
      { name: "_signature", type: "bytes", internalType: "bytes" },
      { name: "_initialLiquidity", type: "uint256", internalType: "uint256" },
    ],
    outputs: [
      { name: "prediction_", type: "address", internalType: "address" },
      { name: "polygonHash_", type: "bytes32", internalType: "bytes32" },
    ],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "getPrediction",
    inputs: [{ name: "_prediction", type: "address", internalType: "address" }],
    outputs: [
      { name: "creator", type: "address", internalType: "address" },
      { name: "resolvers", type: "address[]", internalType: "address[]" },
      { name: "polygonHash", type: "bytes32", internalType: "bytes32" },
      { name: "conditions", type: "string[]", internalType: "string[]" },
      { name: "startDate", type: "uint256", internalType: "uint256" },
      { name: "endDate", type: "uint256", internalType: "uint256" },
      { name: "outcomeSlot", type: "uint256", internalType: "uint256" },
      { name: "resolved", type: "bool", internalType: "bool" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getPredictions",
    inputs: [
      { name: "_offset", type: "uint256", internalType: "uint256" },
      { name: "_limit", type: "uint256", internalType: "uint256" },
    ],
    outputs: [
      {
        name: "",
        type: "tuple[]",
        internalType: "struct IPrediction.Metadata[]",
        components: [
          { name: "polygonId", type: "string", internalType: "string" },
          { name: "polygonCid", type: "string", internalType: "string" },
          { name: "conditions", type: "string[]", internalType: "string[]" },
          { name: "outcomeSlot", type: "uint256", internalType: "uint256" },
          { name: "startDate", type: "uint256", internalType: "uint256" },
          { name: "endDate", type: "uint256", internalType: "uint256" },
          { name: "resolvers", type: "address[]", internalType: "address[]" },
          { name: "resolved", type: "bool", internalType: "bool" },
        ],
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getPredictionsByPolygon",
    inputs: [{ name: "_polygonHash", type: "bytes32", internalType: "bytes32" }],
    outputs: [
      {
        name: "",
        type: "tuple[]",
        internalType: "struct IPrediction.Metadata[]",
        components: [
          { name: "polygonId", type: "string", internalType: "string" },
          { name: "polygonCid", type: "string", internalType: "string" },
          { name: "conditions", type: "string[]", internalType: "string[]" },
          { name: "outcomeSlot", type: "uint256", internalType: "uint256" },
          { name: "startDate", type: "uint256", internalType: "uint256" },
          { name: "endDate", type: "uint256", internalType: "uint256" },
          { name: "resolvers", type: "address[]", internalType: "address[]" },
          { name: "resolved", type: "bool", internalType: "bool" },
        ],
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getTotalPredictions",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "collectFactoryFees",
    inputs: [{ name: "_prediction", type: "address", internalType: "address" }],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "addSafeResolver",
    inputs: [{ name: "_resolver", type: "address", internalType: "address" }],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "owner",
    inputs: [],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "event",
    name: "Factory_PredictionCreated",
    inputs: [
      { name: "prediction", type: "address", indexed: true, internalType: "address" },
      { name: "polygonHash", type: "bytes32", indexed: true, internalType: "bytes32" },
      { name: "conditions", type: "string[]", indexed: false, internalType: "string[]" },
      { name: "startDate", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "endDate", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "outcomeSlot", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "resolvers", type: "address[]", indexed: false, internalType: "address[]" },
      { name: "creator", type: "address", indexed: false, internalType: "address" },
    ],
    anonymous: false,
  },
  {
    type: "error",
    name: "Factory_InvalidSignature",
    inputs: [],
  },
  {
    type: "error",
    name: "Factory_SignatureAlreadyUsed",
    inputs: [],
  },
  {
    type: "error",
    name: "Factory_InsufficientInitialLiquidity",
    inputs: [],
  },
] as const;

// ABI for Prediction contract - individual prediction instances
export const PREDICTION_ABI = [
  {
    type: "function",
    name: "buyTokens",
    inputs: [
      { name: "_outcomeIndex", type: "uint256", internalType: "uint256" },
      { name: "_collateralAmount", type: "uint256", internalType: "uint256" },
    ],
    outputs: [{ name: "tokenAmount_", type: "uint256", internalType: "uint256" }],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "claimRewards",
    inputs: [{ name: "_outcomeIndex", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "payout_", type: "uint256", internalType: "uint256" }],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setResolved",
    inputs: [{ name: "_outcomeIndex", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "success_", type: "bool", internalType: "bool" }],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "getCreator",
    inputs: [],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getMetadata",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "tuple",
        internalType: "struct IPrediction.Metadata",
        components: [
          { name: "polygonId", type: "string", internalType: "string" },
          { name: "polygonCid", type: "string", internalType: "string" },
          { name: "conditions", type: "string[]", internalType: "string[]" },
          { name: "outcomeSlot", type: "uint256", internalType: "uint256" },
          { name: "startDate", type: "uint256", internalType: "uint256" },
          { name: "endDate", type: "uint256", internalType: "uint256" },
          { name: "resolvers", type: "address[]", internalType: "address[]" },
          { name: "resolved", type: "bool", internalType: "bool" },
        ],
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getTotalLiquidity",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getLiquidity",
    inputs: [{ name: "_outcomeIndex", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "event",
    name: "Prediction_TokensBought",
    inputs: [
      { name: "prediction", type: "address", indexed: true, internalType: "address" },
      { name: "outcomeIndex", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "tokenAmount", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "collateralPaid", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "Prediction_RewardClaimed",
    inputs: [
      { name: "user", type: "address", indexed: true, internalType: "address" },
      { name: "amount", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "Prediction_Resolved",
    inputs: [{ name: "outcomeIndex", type: "uint256", indexed: false, internalType: "uint256" }],
    anonymous: false,
  },
  {
    type: "error",
    name: "Prediction_InvalidOutcome",
    inputs: [],
  },
  {
    type: "error",
    name: "Prediction_AlreadyResolved",
    inputs: [],
  },
  {
    type: "error",
    name: "Prediction_NotResolved",
    inputs: [],
  },
] as const;

// ABI for ZeusToken contract - ERC1155 outcome tokens
export const ZEUS_TOKEN_ABI = [
  {
    type: "function",
    name: "balanceOf",
    inputs: [
      { name: "account", type: "address", internalType: "address" },
      { name: "id", type: "uint256", internalType: "uint256" },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "balanceOfBatch",
    inputs: [
      { name: "accounts", type: "address[]", internalType: "address[]" },
      { name: "ids", type: "uint256[]", internalType: "uint256[]" },
    ],
    outputs: [{ name: "", type: "uint256[]", internalType: "uint256[]" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getTokenId",
    inputs: [
      { name: "_prediction", type: "address", internalType: "address" },
      { name: "_outcomeIndex", type: "uint256", internalType: "uint256" },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getTotalSupply",
    inputs: [
      { name: "_prediction", type: "address", internalType: "address" },
      { name: "_outcomeIndex", type: "uint256", internalType: "uint256" },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "s_factoryFeePercent",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "s_creatorFeePercent",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "setApprovalForAll",
    inputs: [
      { name: "operator", type: "address", internalType: "address" },
      { name: "approved", type: "bool", internalType: "bool" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "isApprovedForAll",
    inputs: [
      { name: "account", type: "address", internalType: "address" },
      { name: "operator", type: "address", internalType: "address" },
    ],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "event",
    name: "TransferSingle",
    inputs: [
      { name: "operator", type: "address", indexed: true, internalType: "address" },
      { name: "from", type: "address", indexed: true, internalType: "address" },
      { name: "to", type: "address", indexed: true, internalType: "address" },
      { name: "id", type: "uint256", indexed: false, internalType: "uint256" },
      { name: "value", type: "uint256", indexed: false, internalType: "uint256" },
    ],
    anonymous: false,
  },
] as const;

// ABI for View contract - read-only functions for UI
export const VIEW_ABI = [
  {
    type: "function",
    name: "getFullPredictionInfo",
    inputs: [{ name: "_prediction", type: "address", internalType: "address" }],
    outputs: [
      {
        name: "info_",
        type: "tuple",
        internalType: "struct View.FullPredictionInfo",
        components: [
          { name: "prediction", type: "address", internalType: "address" },
          { name: "creator", type: "address", internalType: "address" },
          { name: "polygonId", type: "string", internalType: "string" },
          { name: "polygonCid", type: "string", internalType: "string" },
          { name: "conditions", type: "string[]", internalType: "string[]" },
          { name: "outcomeSlot", type: "uint256", internalType: "uint256" },
          { name: "startDate", type: "uint256", internalType: "uint256" },
          { name: "endDate", type: "uint256", internalType: "uint256" },
          { name: "resolvers", type: "address[]", internalType: "address[]" },
          { name: "resolved", type: "bool", internalType: "bool" },
          { name: "active", type: "bool", internalType: "bool" },
          { name: "totalLiquidity", type: "uint256", internalType: "uint256" },
          { name: "liquidityPerOutcome", type: "uint256[]", internalType: "uint256[]" },
        ],
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getPredictions",
    inputs: [
      { name: "_offset", type: "uint256", internalType: "uint256" },
      { name: "_limit", type: "uint256", internalType: "uint256" },
    ],
    outputs: [
      {
        name: "",
        type: "tuple[]",
        internalType: "struct IPrediction.Metadata[]",
        components: [
          { name: "polygonId", type: "string", internalType: "string" },
          { name: "polygonCid", type: "string", internalType: "string" },
          { name: "conditions", type: "string[]", internalType: "string[]" },
          { name: "outcomeSlot", type: "uint256", internalType: "uint256" },
          { name: "startDate", type: "uint256", internalType: "uint256" },
          { name: "endDate", type: "uint256", internalType: "uint256" },
          { name: "resolvers", type: "address[]", internalType: "address[]" },
          { name: "resolved", type: "bool", internalType: "bool" },
        ],
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getPredictionsByPolygon",
    inputs: [{ name: "_polygonHash", type: "bytes32", internalType: "bytes32" }],
    outputs: [
      {
        name: "",
        type: "tuple[]",
        internalType: "struct IPrediction.Metadata[]",
        components: [
          { name: "polygonId", type: "string", internalType: "string" },
          { name: "polygonCid", type: "string", internalType: "string" },
          { name: "conditions", type: "string[]", internalType: "string[]" },
          { name: "outcomeSlot", type: "uint256", internalType: "uint256" },
          { name: "startDate", type: "uint256", internalType: "uint256" },
          { name: "endDate", type: "uint256", internalType: "uint256" },
          { name: "resolvers", type: "address[]", internalType: "address[]" },
          { name: "resolved", type: "bool", internalType: "bool" },
        ],
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getTotalPredictions",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
] as const;

// ERC20 ABI for collateral token (USDC)
export const ERC20_ABI = [
  {
    inputs: [
      { name: "spender", type: "address" },
      { name: "amount", type: "uint256" },
    ],
    name: "approve",
    outputs: [{ name: "", type: "bool" }],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      { name: "owner", type: "address" },
      { name: "spender", type: "address" },
    ],
    name: "allowance",
    outputs: [{ name: "", type: "uint256" }],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [{ name: "account", type: "address" }],
    name: "balanceOf",
    outputs: [{ name: "", type: "uint256" }],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "decimals",
    outputs: [{ name: "", type: "uint8" }],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      { name: "from", type: "address" },
      { name: "to", type: "address" },
      { name: "value", type: "uint256" },
    ],
    name: "transferFrom",
    outputs: [{ name: "", type: "bool" }],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

// Smart contract configuration
export const SMART_CONTRACT_CONFIG: SmartContractConfig = {
  factoryAddress: process.env.FACTORY_ADDRESS || "0x4e50924c6301B5fC944397750B4e79E53E65Bf44",
  viewAddress: process.env.VIEW_ADDRESS || "0xA3580BDCc2f569A30e8B92DEb0662692447Ef50D",
  tokenAddress: process.env.ZEUS_TOKEN_ADDRESS || "0x9ee9c248196C2Fd206F33C556FB29627dc8B9122",
  collateralTokenAddress: process.env.COLLATERAL_TOKEN_ADDRESS || "0x771C5E2b933994C50B5935b48bf519263C31d089",
  rebalancerAddress: process.env.REBALANCER_ADDRESS || "0xE93c617920a6560068E6366a89fd61c8d9643AcE",
  resolverAddress: process.env.RESOLVER_ADDRESS || "0x5CEBdF8C3a6Cbbf2ad9c71C6b0a449452940FFF1",
  chainId: parseInt(process.env.CHAIN_ID || "1"),
};

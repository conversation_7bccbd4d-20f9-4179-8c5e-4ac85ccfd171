import { LayerSpecification } from "mapbox-gl";
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, LineLayer, ProjectionSpecification } from "mapbox-gl";

export const BASE_URL = process.env.NEXT_PUBLIC_API_URL ?? "";
export const PRIVY_APPID = process.env.PRIVY_APPID ?? "";
export const MAPS_APIKEY = process.env.GOOGLE_MAPS_KEY ?? "";
export const MAPBOX_TOKEN = process.env.MAPBOX_TOKEN ?? "";
export const TOMORROW_API_KEY = process.env.TOMORROW_API_KEY ?? "";
export const NASA_FIRMS_API_KEY = process.env.NASA_FIRMS_API_KEY ?? "";
export const GEODATIN_STYLE_URL = "mapbox://styles/geodatin/cljod7u7k00ky01que93y0lx0?optimize=true";
export const DARK_STYLE_URL = "mapbox://styles/geodatin/clawpmxqa000014mrgrn39mtd?optimize=true";
export const STREETS_STYLE_URL = "mapbox://styles/mapbox/streets-v12";
export const OUTDOORS_STYLE_URL = "mapbox://styles/mapbox/outdoors-v12";
export const LIGHT_STYLE_URL = "mapbox://styles/mapbox/light-v11";
export const NAVIGATION_DAY_STYLE_URL = "mapbox://styles/mapbox/navigation-day-v1";
export const NAVIGATION_NIGHT_STYLE_URL = "mapbox://styles/mapbox/navigation-night-v1";
export const DETECT_COUNTRY_LOCAL_STORAGE_KEY = "@yby.energy/country";
export const DETECT_CITY_LOCAL_STORAGE_KEY = "@yby.energy/city";
export const DETECT_LATITUDE_LOCAL_STORAGE_KEY = "@yby.energy/latitude";
export const DETECT_LONGITUDE_LOCAL_STORAGE_KEY = "@yby.energy/longitude";
export const INITIAL_COUNTRY_ZOOM = 2.5;
export const OPENWEATHER_API_KEY = process.env.OPENWEATHER_API_KEY ?? "";
export const PROPERTIES_MIN_ZOOM = 3;
export const PROPERTIES_MAX_ZOOM = 22;
export const DEFAULT_MAP_ZOOM = 10;
export const TERRAIN_EXAGGERATION = 1.9;
export const LANDING_REDIRECT_URL = "https://ibi.cash";

export const STATIC_WIND_LAYER: LayerSpecification = {
  id: "wind-layer",
  type: "raster-particle",
  source: "raster-array-source",
  "source-layer": "10winds",
  paint: {
    "raster-particle-speed-factor": 0.4,
    "raster-particle-fade-opacity-factor": 0.9,
    "raster-particle-reset-rate-factor": 0.4,
    "raster-particle-count": 4000,
    "raster-particle-max-speed": 40,
    "raster-particle-color": [
      "interpolate",
      ["linear"],
      ["raster-particle-speed"],
      1.5,
      "rgba(134,163,171,256)",
      2.5,
      "rgba(126,152,188,256)",
      4.12,
      "rgba(110,143,208,256)",
      4.63,
      "rgba(110,143,208,256)",
      6.17,
      "rgba(15,147,167,256)",
      7.72,
      "rgba(15,147,167,256)",
      9.26,
      "rgba(57,163,57,256)",
      10.29,
      "rgba(57,163,57,256)",
      11.83,
      "rgba(194,134,62,256)",
      13.37,
      "rgba(194,134,63,256)",
      14.92,
      "rgba(200,66,13,256)",
      16.46,
      "rgba(200,66,13,256)",
      18.0,
      "rgba(210,0,50,256)",
      20.06,
      "rgba(215,0,50,256)",
      21.6,
      "rgba(175,80,136,256)",
      23.66,
      "rgba(175,80,136,256)",
      25.21,
      "rgba(117,74,147,256)",
      27.78,
      "rgba(117,74,147,256)",
      29.32,
      "rgba(68,105,141,256)",
      31.89,
      "rgba(68,105,141,256)",
      33.44,
      "rgba(194,251,119,256)",
      42.18,
      "rgba(194,251,119,256)",
      43.72,
      "rgba(241,255,109,256)",
      48.87,
      "rgba(241,255,109,256)",
      50.41,
      "rgba(256,256,256,256)",
      57.61,
      "rgba(256,256,256,256)",
      59.16,
      "rgba(0,256,256,256)",
      68.93,
      "rgba(0,256,256,256)",
      69.44,
      "rgba(256,37,256,256)",
    ],
  },
};

export const BIOME_COLORS = [
  "#1E88E5",
  "#43A047",
  "#E53935",
  "#FB8C00",
  "#8E24AA",
  "#00ACC1",
  "#FFB300",
  "#5E35B1",
  "#3949AB",
  "#039BE5",
  "#00897B",
  "#7CB342",
  "#C0CA33",
  "#FDD835",
  "#F4511E",
  "#6D4C41",
  "#546E7A",
  "#EC407A",
];

export const navigationOptions = [
  {
    icon: "ic:baseline-travel-explore",
    label: "Explore",
    pathname: "/explore",
    subpaths: [
      {
        icon: "ph:users-bold",
        label: "Users",
        pathname: "/explore/users",
      },
      {
        icon: "lucide:land-plot",
        label: "Landunits",
        pathname: "/explore/landunits",
      },
      {
        icon: "solar:medal-star-bold-duotone",
        label: "Quests",
        pathname: "/explore/quests",
      },
    ],
  },
  {
    icon: "fluent-mdl2:world",
    label: "Claim",
    pathname: "/claim",
  },
  {
    icon: "ic:baseline-book",
    label: "Bond",
    pathname: "/bond",
  },
  {
    icon: "ic:baseline-rocket",
    label: "Pump",
    pathname: "/pump",
  },
  {
    icon: "ic:baseline-water-drop",
    label: "Predict",
    pathname: "/predict",
  },
];

export const MAP_PROJECTION: ProjectionSpecification = {
  name: "globe",
};

export const propertyLayer: FillLayer = {
  id: "properties",
  type: "fill",
  source: "properties-source",
  "source-layer": "properties",
  paint: {
    "fill-color": "#0288d1",
    "fill-opacity": ["case", ["boolean", ["feature-state", "selected"], false], 0, 0.6],
    "fill-outline-color": "#ffffff",
  },
  minzoom: PROPERTIES_MIN_ZOOM,
};

export const propertyOutlineLayer: LineLayer = {
  id: "property-outline",
  type: "line",
  source: "properties-source",
  "source-layer": "properties",
  paint: {
    "line-color": ["case", ["boolean", ["feature-state", "selected"], false], "#768FFF", "#ffffff"],
    "line-width": ["case", ["boolean", ["feature-state", "selected"], false], 3, 1],
    "line-width-transition": {
      duration: 300,
    },
    "line-opacity": 1,
  },
  minzoom: PROPERTIES_MIN_ZOOM,
};

export const maskLayer: FillLayer = {
  id: "mask",
  type: "fill",
  source: "selected-feature",
  paint: {
    "fill-color": "#000000",
    "fill-opacity": 0.5,
  },
};

import { BASE_URL } from "@/utils/constants";
import { handleStorage } from "@/utils/storage";
import axios from "axios";

const baseURL = BASE_URL;

export const api = axios.create({
  baseURL,
  headers: {
    "Content-Type": "application/json",
  },
});

api.interceptors.request.use((config) => {
  const token = handleStorage<string>("local", "privy:token", "get");
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

api.interceptors.response.use(
  (response) => response,
  async (error) => {
    console.log(error);
    if (error?.response?.status === 401) {
      // Set cookie to indicate unauthorized login attempt
      try {
        await fetch("/api/session", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ value: "UNAUTHORIZED_LOGIN_ATTEMPT" }),
        });
      } catch (cookieError) {
        console.error("Failed to set unauthorized cookie:", cookieError);
      }

      window.location.href = "/force-logout";
    }

    return Promise.reject(error);
  },
);

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Desabilita SSG para resolver problemas com useSearchParams
  experimental: {
    missingSuspenseWithCSRBailout: false,
  },
  images: {
    domains: ["api.mapbox.com", "api.yby.energy"],
    remotePatterns: [
      {
        protocol: "https",
        hostname: "cdn.dribbble.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "ibi.cash",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "api.mapbox.com",
        pathname: "/**",
      },
    ],
  },
  async headers() {
    return [
      {
        source: "/api/map-image/:ibicode",
        headers: [
          {
            key: "Access-Control-Allow-Origin",
            value: "*",
          },
        ],
      },
    ];
  },
  env: {
    PRIVY_APPID: process.env.PRIVY_APPID,
    MAPS_APIKEY: process.env.MAPS_APIKEY,
    MAPBOX_TOKEN: process.env.MAPBOX_TOKEN,
    TOMORROW_API_KEY: process.env.TOMORROW_API_KEY,
    GOOGLE_MAPS_KEY: process.env.GOOGLE_MAPS_KEY,
    NASA_FIRMS_API_KEY: process.env.NASA_FIRMS_API_KEY,
    OPENWEATHER_API_KEY: process.env.OPENWEATHER_API_KEY,
  },
  staticPageGenerationTimeout: 180, // aumenta para 180 segundos
  webpack: (config) => {
    config.module.rules.push({
      test: /\.svg$/,
      issuer: /\.[jt]sx?$/,
      use: ["@svgr/webpack"],
    });
    return config;
  },
};

export default nextConfig;

{"abi": [{"type": "constructor", "inputs": [{"name": "_factory", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getCreator", "inputs": [{"name": "_prediction", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getFullPredictionInfo", "inputs": [{"name": "_prediction", "type": "address", "internalType": "address"}], "outputs": [{"name": "info_", "type": "tuple", "internalType": "struct View.FullPredictionInfo", "components": [{"name": "prediction", "type": "address", "internalType": "address"}, {"name": "creator", "type": "address", "internalType": "address"}, {"name": "polygonId", "type": "string", "internalType": "string"}, {"name": "polygonCid", "type": "string", "internalType": "string"}, {"name": "conditions", "type": "string[]", "internalType": "string[]"}, {"name": "outcomeSlot", "type": "uint256", "internalType": "uint256"}, {"name": "startDate", "type": "uint256", "internalType": "uint256"}, {"name": "endDate", "type": "uint256", "internalType": "uint256"}, {"name": "resolvers", "type": "address[]", "internalType": "address[]"}, {"name": "resolved", "type": "bool", "internalType": "bool"}, {"name": "active", "type": "bool", "internalType": "bool"}, {"name": "totalLiquidity", "type": "uint256", "internalType": "uint256"}, {"name": "liquidityPerOutcome", "type": "uint256[]", "internalType": "uint256[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "getLiquidity", "inputs": [{"name": "_prediction", "type": "address", "internalType": "address"}, {"name": "_outcomeIndex", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getMetadata", "inputs": [{"name": "_prediction", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IPrediction.Metadata", "components": [{"name": "polygonId", "type": "string", "internalType": "string"}, {"name": "polygonCid", "type": "string", "internalType": "string"}, {"name": "conditions", "type": "string[]", "internalType": "string[]"}, {"name": "outcomeSlot", "type": "uint256", "internalType": "uint256"}, {"name": "startDate", "type": "uint256", "internalType": "uint256"}, {"name": "endDate", "type": "uint256", "internalType": "uint256"}, {"name": "resolvers", "type": "address[]", "internalType": "address[]"}, {"name": "resolved", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "getPendingResolutions", "inputs": [], "outputs": [{"name": "", "type": "tuple[]", "internalType": "struct IPrediction.Metadata[]", "components": [{"name": "polygonId", "type": "string", "internalType": "string"}, {"name": "polygonCid", "type": "string", "internalType": "string"}, {"name": "conditions", "type": "string[]", "internalType": "string[]"}, {"name": "outcomeSlot", "type": "uint256", "internalType": "uint256"}, {"name": "startDate", "type": "uint256", "internalType": "uint256"}, {"name": "endDate", "type": "uint256", "internalType": "uint256"}, {"name": "resolvers", "type": "address[]", "internalType": "address[]"}, {"name": "resolved", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "getPrediction", "inputs": [{"name": "_prediction", "type": "address", "internalType": "address"}], "outputs": [{"name": "creator_", "type": "address", "internalType": "address"}, {"name": "resolvers_", "type": "address[]", "internalType": "address[]"}, {"name": "polygonHash_", "type": "bytes32", "internalType": "bytes32"}, {"name": "conditions_", "type": "string[]", "internalType": "string[]"}, {"name": "startDate_", "type": "uint256", "internalType": "uint256"}, {"name": "endDate_", "type": "uint256", "internalType": "uint256"}, {"name": "outcomeSlot_", "type": "uint256", "internalType": "uint256"}, {"name": "resolved_", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "getPredictions", "inputs": [{"name": "_offset", "type": "uint256", "internalType": "uint256"}, {"name": "_limit", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple[]", "internalType": "struct IPrediction.Metadata[]", "components": [{"name": "polygonId", "type": "string", "internalType": "string"}, {"name": "polygonCid", "type": "string", "internalType": "string"}, {"name": "conditions", "type": "string[]", "internalType": "string[]"}, {"name": "outcomeSlot", "type": "uint256", "internalType": "uint256"}, {"name": "startDate", "type": "uint256", "internalType": "uint256"}, {"name": "endDate", "type": "uint256", "internalType": "uint256"}, {"name": "resolvers", "type": "address[]", "internalType": "address[]"}, {"name": "resolved", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "getPredictionsByPolygon", "inputs": [{"name": "_polygonHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "tuple[]", "internalType": "struct IPrediction.Metadata[]", "components": [{"name": "polygonId", "type": "string", "internalType": "string"}, {"name": "polygonCid", "type": "string", "internalType": "string"}, {"name": "conditions", "type": "string[]", "internalType": "string[]"}, {"name": "outcomeSlot", "type": "uint256", "internalType": "uint256"}, {"name": "startDate", "type": "uint256", "internalType": "uint256"}, {"name": "endDate", "type": "uint256", "internalType": "uint256"}, {"name": "resolvers", "type": "address[]", "internalType": "address[]"}, {"name": "resolved", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "getResolvablePredictions", "inputs": [{"name": "_offset", "type": "uint256", "internalType": "uint256"}, {"name": "_limit", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple[]", "internalType": "struct IPrediction.Metadata[]", "components": [{"name": "polygonId", "type": "string", "internalType": "string"}, {"name": "polygonCid", "type": "string", "internalType": "string"}, {"name": "conditions", "type": "string[]", "internalType": "string[]"}, {"name": "outcomeSlot", "type": "uint256", "internalType": "uint256"}, {"name": "startDate", "type": "uint256", "internalType": "uint256"}, {"name": "endDate", "type": "uint256", "internalType": "uint256"}, {"name": "resolvers", "type": "address[]", "internalType": "address[]"}, {"name": "resolved", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "getTotalLiquidity", "inputs": [{"name": "_prediction", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getTotalPredictions", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getTotalPredictionsByPolygon", "inputs": [{"name": "_polygonHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "isActive", "inputs": [{"name": "_prediction", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "error", "name": "View_InvalidOutcome", "inputs": []}]}
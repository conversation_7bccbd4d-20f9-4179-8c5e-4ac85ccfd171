// SPDX-License-Identifier: MIT
pragma solidity 0.8.26;

interface IPrediction {
    struct Metadata {
        // REVIEW: need add a string question here, for better clarify of the prediction
        string polygonId;
        string polygonCid;
        string[] conditions; // ex: ["<10t", "10–20t", ">20t"]
        uint256 outcomeSlot;
        uint256 startDate;
        uint256 endDate;
        address[] resolvers;
        // REVIEW: resolved cannot setted here, because need block setting resolve = true on prediction create
        bool resolved;
    }

    function createPool(
        Metadata calldata _input,
        address _creator,
        address _factory,
        address _collateralToken,
        address _rebalancer,
        address _token
    ) external;

    function buyTokens(uint256 _outcomeIndex, uint256 _collateralAmount) external returns (uint256);

    function setResolved(uint256 _outcomeIndex) external returns(bool);

    function claimRewards(uint256 _outcomeIndex) external;

    function collectFactoryFees() external;

    function collectCreatorFees() external;

    function getCreator() external view returns (address);

    function getMetadata() external view returns (Metadata calldata);

    function isActive() external view returns (bool);

    function getTotalLiquidity() external view returns (uint256);

    function getLiquidity(uint256 _outcomeIndex) external view returns (uint256);

    function getTotalFeesByFactory() external view returns (uint256);

    function getTotalFeesByCreator() external view returns (uint256);
}
// SPDX-License-Identifier: MIT
pragma solidity 0.8.26;

import { IERC20 } from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import { ReentrancyGuard } from "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import { Initializable } from "@openzeppelin/contracts/proxy/utils/Initializable.sol";
import { ERC1155Holder } from "@openzeppelin/contracts/token/ERC1155/utils/ERC1155Holder.sol";
import { IPrediction } from "./interfaces/IPrediction.sol";
import { IRebalancer } from "./interfaces/IRebalancer.sol";
import { IZeusToken } from "./interfaces/IToken.sol";

/**
 * @title Prediction
 */
contract Prediction is ReentrancyGuard, ERC1155Holder, Initializable  {
    IPrediction.Metadata s_meta;
    IERC20 s_collateralToken;
    IRebalancer s_rebalancer;
    IZeusToken s_token;

    address s_creator;
    address s_factory;
    uint256 s_totalLiquidity;
    uint256 s_winningOutcomes;
    uint256 s_totalFactoryFees;
    uint256 s_totalCreatorFees;
    bool s_factoryFeesCollected;
    bool s_creatorFeesCollected;

    // REVIEW: add uint8 constant MAX_OUTCOME_SLOTS = type(uint8).max;

    mapping ( uint256 outcomeIndex => uint256 realLiquidity ) m_liquidity;

    // Events
    event Prediction_PoolCreated(address indexed prediction, address s_creator);
    event Prediction_TokensBought(
        address indexed prediction,
        uint256 outcomeIndex,
        uint256 tokenAmount,
        uint256 collateralPaid
    );
    event Prediction_LiquidityUpdated(
        uint256 outcome,
        uint256 outcomeLiquidityAdd,
        uint256 totalOutcomeLiquidity,
        uint256 totalPredictionLiquidity
    );
    event Prediction_RewardClaimed(address indexed user, uint256 amount);
    event Prediction_Resolved(uint256 outcomeIndex);
    event Prediction_FeesCollected(address indexed sender, address prediction, uint256 amount);

    /// @notice Gas Savings: since Solidity 0.8.4, custom errors in most gas-efficient
    error Prediction_NotResolved();
    error Prediction_PoolNotActive();
    error Prediction_InvalidAddress();
    error Prediction_UnsafeResolver();
    error Prediction_InvalidOutcome();
    error Prediction_AlreadyResolved();
    error Prediction_NoTokensToClaim();
    error Prediction_PoolAlreadyExists();
    error Prediction_PredictionNotEnded();
    error Prediction_AmountCannotBeZero();
    error Prediction_FeesAlreadyCollected();
    error Prediction_PredictionNotResolved();
    error Prediction_InsufficientLiquidity();

    modifier onlyFactory {
        if (msg.sender != s_factory) revert Prediction_InvalidAddress();
        _;
    }

    modifier onlyCreator {
        if (msg.sender != s_creator) revert Prediction_InvalidAddress();
        _;
    }

    function createPool(
        IPrediction.Metadata calldata _input,
        address _creator,
        address _factory,
        address _collateralToken,
        address _rebalancer,
        address _token
    ) external initializer {  
        if (
            _creator == address(0) ||
            _factory == address(0) ||
            _collateralToken == address(0) ||
            _rebalancer == address(0) ||
            _token == address(0)
        ) revert Prediction_InvalidAddress();

        s_meta = _input;
        s_creator = _creator;
        s_factory = _factory;
        s_collateralToken = IERC20(_collateralToken);
        s_rebalancer = IRebalancer(_rebalancer);
        s_token = IZeusToken(_token);

        for (uint256 i = 0; i < _input.outcomeSlot; i++) {
            m_liquidity[i] = 1;
        }

        s_token.registerPrediction();
        emit Prediction_PoolCreated(address(this), s_creator);
    }

    function buyTokens(
        uint256 _outcomeIndex, uint256 _collateralAmount
    ) external nonReentrant returns (uint256 tokenAmount_) {
        if (_collateralAmount == 0) revert Prediction_AmountCannotBeZero();
        if (_outcomeIndex > s_meta.outcomeSlot) revert Prediction_InvalidOutcome();
        if (s_meta.resolved) revert Prediction_AlreadyResolved();

        require(
            s_collateralToken.transferFrom(msg.sender, address(this), _collateralAmount),
            "Collateral transfer failed"
        );

        m_liquidity[_outcomeIndex] += _collateralAmount;
        s_totalLiquidity += _collateralAmount;

        tokenAmount_ = s_rebalancer.distribution(
            _applyFees(_collateralAmount),
            m_liquidity[_outcomeIndex],
            s_totalLiquidity
            /* uint256 _outcomeSlot */
        );
        if (tokenAmount_ == 0) revert Prediction_InsufficientLiquidity();
        emit Prediction_LiquidityUpdated(_outcomeIndex, _collateralAmount, m_liquidity[_outcomeIndex], s_totalLiquidity);

        // REVIEW: setted for integration test, but need verify if this correct logic, maybe isnt the better solution setting if (msg.sender == s_factory)
        if (msg.sender == s_factory) {
            _mintTokens(s_creator, _outcomeIndex, tokenAmount_);
        } else {
            _mintTokens(msg.sender, _outcomeIndex, tokenAmount_);
        }

        emit Prediction_TokensBought(address(this), _outcomeIndex, tokenAmount_, _collateralAmount);
        return tokenAmount_;
    }

    function setResolved(uint256 _outcomeIndex) external returns (bool success_) {
        bool isResolver = false;
        for (uint256 i = 0; i < s_meta.resolvers.length; i++) {
            if (msg.sender == s_meta.resolvers[i]) {
                isResolver = true;
                break;
            }
        }
        if (!isResolver) revert Prediction_UnsafeResolver();
        if (s_meta.resolved) revert Prediction_AlreadyResolved();
        if (_outcomeIndex > s_meta.outcomeSlot) revert Prediction_InvalidOutcome();
        if (block.timestamp <= s_meta.endDate) revert Prediction_PredictionNotEnded();

        s_meta.resolved = true;
        s_winningOutcomes = _outcomeIndex;
        // REVIEW: need s_meta.burnTokens() all losing tokens, need search a great solution for gas savings and user participation on burning process
        emit Prediction_Resolved(_outcomeIndex);
        return success_ = s_meta.resolved;
    }

    function claimRewards(uint256 _outcomeIndex) external nonReentrant returns (uint256 payout_) {
        /*
         REVIEW: ensure winning tokens are redeemable for a fixed amount, a standard in prediction markets where winning tokens are worth $1, and losing tokens are worth $0, ensuring a fixed value per token.
         REVIEW: adjust redemption to distribute the total available collateral (after fees) proportionally among winning token holders.
         REVIEW: calculating the payout as (tokenBalance / totalWinningTokens) * (s_totalLiquidity - s_totalFactoryFees - s_totalCreatorFees), where totalWinningTokens is the total supply of the winning outcome token is correct in this case?
        */
        if (!s_meta.resolved) revert Prediction_NotResolved();
        if (_outcomeIndex != s_winningOutcomes) revert Prediction_InvalidOutcome();

        uint256 tokenId = s_token.getTokenId(address(this), _outcomeIndex);
        uint256 tokenBalance = s_token.balanceOf(msg.sender, tokenId);
        if (tokenBalance == 0) revert Prediction_NoTokensToClaim();

        uint256 totalWinningTokens = s_token.getTotalSupply(address(this), _outcomeIndex);
        if (totalWinningTokens == 0) revert Prediction_NoTokensToClaim();

        uint256 availableCollateral = s_totalLiquidity - s_totalFactoryFees - s_totalCreatorFees;
        payout_ = (tokenBalance * availableCollateral) / totalWinningTokens;

        // REVIEW: Alert! need owner to call burnTokens() in the Token contract.
        s_token.burnTokens(msg.sender, _outcomeIndex, tokenBalance);

        require(
            s_collateralToken.transfer(msg.sender, payout_),
            "Reward transfer failed"
        );

        emit Prediction_RewardClaimed(msg.sender, payout_);
        return payout_;
    }

    // REVIEW: Alert! need call this function from Factory.sol by interface - onlyFactory.
    function collectFactoryFees() external onlyFactory nonReentrant {
        if (!s_meta.resolved) revert Prediction_PredictionNotResolved();
        if (s_totalFactoryFees == 0) revert Prediction_AmountCannotBeZero();
        if (s_factoryFeesCollected) revert Prediction_FeesAlreadyCollected();

        uint256 value = s_totalFactoryFees;
        s_totalFactoryFees = 0;
        s_factoryFeesCollected = true;
        require(
            s_collateralToken.transfer(s_factory, value),
            "Transfer to factory failed"
        );

        emit Prediction_FeesCollected(msg.sender, address(this), value);
    }

    function collectCreatorFees() external onlyCreator nonReentrant {
        if (!s_meta.resolved) revert Prediction_PredictionNotResolved();
        if (s_totalCreatorFees == 0) revert Prediction_AmountCannotBeZero();
        if (s_creatorFeesCollected) revert Prediction_FeesAlreadyCollected();

        uint256 value = s_totalCreatorFees;
        s_totalCreatorFees = 0;
        s_creatorFeesCollected = true;
        require(
            s_collateralToken.transfer(s_creator, value),
            "Transfer to s_creator failed"
        );

        emit Prediction_FeesCollected(msg.sender, address(this), value);
    }

    function getCreator() external view returns (address) {
        return s_creator;
    }

    function getMetadata() external view returns (IPrediction.Metadata memory) {
        return s_meta;
    }

    function getTotalLiquidity() external view returns (uint256) {
        return s_totalLiquidity;
    }

    function getLiquidity(uint256 _outcomeIndex) external view returns (uint256) {
        if (_outcomeIndex > s_meta.outcomeSlot) revert Prediction_InvalidOutcome();
        return m_liquidity[_outcomeIndex];
    }

    function getTotalFeesByFactory() external view returns (uint256) {
        return s_totalFactoryFees;
    }

    function getTotalFeesByCreator() external view returns (uint256) {
        return s_totalCreatorFees;
    }

    function _applyFees(uint256 _amount) internal returns (uint256 netAmount_) {
        uint256 factoryFee = (_amount * s_token.s_factoryFeePercent()) / 10000;
        uint256 creatorFee = (_amount * s_token.s_creatorFeePercent()) / 10000;
        s_totalFactoryFees += factoryFee;
        s_totalCreatorFees += creatorFee;
        return _amount - factoryFee - creatorFee;
    }

    function _mintTokens(address _sender, uint256 _outcomeIndex, uint256 _tokenAmount) internal {
        s_token.mintTokens(_sender, _outcomeIndex, _tokenAmount);
    }
}
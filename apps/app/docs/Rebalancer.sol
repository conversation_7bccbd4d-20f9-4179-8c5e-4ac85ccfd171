// SPDX-License-Identifier: MIT
pragma solidity 0.8.26;

contract Rebalancer {
    error Rebalancer_AmountCannotBeZero();

    // REVIEW: ensure maintains the PM-AMM principle where prices reflect the ratio of outcome liquidity to total liquidity.
    function distribution(
        uint256 _netCollateral,
        uint256 _outcomeLiquidity,
        uint256 _totalLiquidity
        // REVIEW: outcomeSlot is not used in this function, for better exaction of the PM-AMM model? it should be used to determine the outcomes prices.
        /* uint256 _outcomeSlot */
    ) external pure returns (uint256 tokenAmount_) {
        // REVIEW: distribution needs aligning with a PM-AMM model like Paradigm's pm-AMM.
        if (_netCollateral == 0 ) revert Rebalancer_AmountCannotBeZero();

        // REVIEW: ensure decimals of the collateral tokens and outcome tokens
        uint256 outcomePrice = (_outcomeLiquidity * 1e18) / _totalLiquidity;
        if (outcomePrice == 0) revert Rebalancer_AmountCannotBeZero();

        tokenAmount_ = (_netCollateral * 1e18) / outcomePrice;
        return tokenAmount_;
    }
}
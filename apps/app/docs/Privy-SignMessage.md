Ethereum
Sign a message
This method uses Ethereum’s personal_sign RPC method. If you are looking for a low-level raw signature over a input hash, see secp256k1_sign.

React
React Native
Swift
Android
Unity
Flutter
NodeJS
REST API
Use the signMessage method exported from the useSignMessage hook to sign a message with an Ethereum embedded wallet.

​
Usage

Copy

Ask AI
import {useSignMessage} from '@privy-io/react-auth';

const {signMessage} = useSignMessage();

const uiOptions = {
title: 'You are voting for foobar project'
};

const {signature} = await signMessage({message: 'I hereby vote for foobar'}, {uiOptions});
​
Parameters
​
message
stringrequired
Message to be signed.

​
options
Object

Hide properties

​
uiOptions
SignMessageModalUIOptions
UI options to customize the signature prompt modal. Learn more

To hide confirmation modals, set options.uiOptions.showWalletUIs to false. Learn more about configuring modal prompts here.

​
address
string
Address for the embedded wallet signing the message. Only set this parameter if using imported embedded wallets or multiple HD embedded wallets for the same user. Defaults to the user’s embedded wallet at HD index 0.

​
Returns
​
signature
string
The signature produced by the wallet.

​
Callbacks
Configure callbacks for Privy’s signMessage method on the useSignMessage hook:

Copy

Ask AI
import {useSignMessage} from '@privy-io/react-auth';

const {signMessage} = useSignMessage({
onSuccess: ({signature}) => {
console.log(signature);
// Any logic you'd like to execute after a user successfully signs a message
},
onError: (error) => {
console.log(error);
// Any logic you'd like to execute after a user exits the message signing flow or there is an error
}
});

// Then call `signMessage` in your code, which will invoke these callbacks on completion
As parameters to useSignMessage, you may include an onSuccess callback and/or an onError callback.

While this component is mounted, any invocation of signMessage will trigger the onSuccess callback or onError callback on completion, depending on if the message was successfully signed or not.

​
onSuccess
If set, the onSuccess callback will execute after a user has successfully signed the message. Within this callback, you can access a signature parameter, which is the signature string value generated by the wallet to sign the message.

​
onError
If set, the onError callback will execute after a user attempts to sign a message and there is an error, or if the user exits the signature flow prematurely. Within this callback, you may access an error code with more information about the error.

Looking to send USDC or another ERC-20 token? See our Send USDC recipe.

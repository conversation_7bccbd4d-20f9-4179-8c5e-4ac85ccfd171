Ethereum
Send a transaction
React
React Native
Swift
Android
Unity
Flutter
NodeJS
REST API
Python
To send a transaction from a wallet using the React SDK, use the sendTransaction method from the useSendTransaction hook:

Copy

Ask AI
sendTransaction: (input: UnsignedTransactionRequest, options?: SendTransactionOptions) => Promise<{ hash: HexString }>
​
Usage

Copy

Ask AI
import {useSendTransaction} from '@privy-io/react-auth';
const {sendTransaction} = useSendTransaction();
sendTransaction({
to: '******************************************',
value: 100000
});
​
Parameters
​
input
UnsignedTransactionRequestrequired
The details of the transaction to send on the chain.

​
options.uiOptions
SendTransactionModalUIOptions
The options for the UI of the send transaction modal. Learn more.

To hide confirmation modals, set options.uiOptions.showWalletUIs to false. Learn more about configuring modal prompts here.

​
options.fundWalletConfig
FundWalletConfig
The configuration for funding the wallet.

​
options.address
string
The address of the wallet to use for sending the transaction.

​
Returns
​
hash
HexString
The hash for the broadcasted transaction.

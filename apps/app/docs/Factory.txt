{"abi": [{"type": "constructor", "inputs": [{"name": "_collateralToken", "type": "address", "internalType": "address"}, {"name": "_rebalancer", "type": "address", "internalType": "address"}, {"name": "_template", "type": "address", "internalType": "address"}, {"name": "_token", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "addSafeResolver", "inputs": [{"name": "_resolver", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "collectFactoryFees", "inputs": [{"name": "_prediction", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "createPrediction", "inputs": [{"name": "_input", "type": "tuple", "internalType": "struct IPrediction.Metadata", "components": [{"name": "polygonId", "type": "string", "internalType": "string"}, {"name": "polygonCid", "type": "string", "internalType": "string"}, {"name": "conditions", "type": "string[]", "internalType": "string[]"}, {"name": "outcomeSlot", "type": "uint256", "internalType": "uint256"}, {"name": "startDate", "type": "uint256", "internalType": "uint256"}, {"name": "endDate", "type": "uint256", "internalType": "uint256"}, {"name": "resolvers", "type": "address[]", "internalType": "address[]"}, {"name": "resolved", "type": "bool", "internalType": "bool"}]}, {"name": "_signature", "type": "bytes", "internalType": "bytes"}, {"name": "_initialLiquidity", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "prediction_", "type": "address", "internalType": "address"}, {"name": "polygonHash_", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getPendingResolutions", "inputs": [], "outputs": [{"name": "", "type": "tuple[]", "internalType": "struct IPrediction.Metadata[]", "components": [{"name": "polygonId", "type": "string", "internalType": "string"}, {"name": "polygonCid", "type": "string", "internalType": "string"}, {"name": "conditions", "type": "string[]", "internalType": "string[]"}, {"name": "outcomeSlot", "type": "uint256", "internalType": "uint256"}, {"name": "startDate", "type": "uint256", "internalType": "uint256"}, {"name": "endDate", "type": "uint256", "internalType": "uint256"}, {"name": "resolvers", "type": "address[]", "internalType": "address[]"}, {"name": "resolved", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "getPolygonHash", "inputs": [{"name": "_prediction", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "getPrediction", "inputs": [{"name": "_prediction", "type": "address", "internalType": "address"}], "outputs": [{"name": "creator", "type": "address", "internalType": "address"}, {"name": "resolvers", "type": "address[]", "internalType": "address[]"}, {"name": "polygonHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "conditions", "type": "string[]", "internalType": "string[]"}, {"name": "startDate", "type": "uint256", "internalType": "uint256"}, {"name": "endDate", "type": "uint256", "internalType": "uint256"}, {"name": "outcomeSlot", "type": "uint256", "internalType": "uint256"}, {"name": "resolved", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "getPredictionStruct", "inputs": [{"name": "_prediction", "type": "address", "internalType": "address"}], "outputs": [{"name": "prediction_", "type": "tuple", "internalType": "struct IPrediction.Metadata", "components": [{"name": "polygonId", "type": "string", "internalType": "string"}, {"name": "polygonCid", "type": "string", "internalType": "string"}, {"name": "conditions", "type": "string[]", "internalType": "string[]"}, {"name": "outcomeSlot", "type": "uint256", "internalType": "uint256"}, {"name": "startDate", "type": "uint256", "internalType": "uint256"}, {"name": "endDate", "type": "uint256", "internalType": "uint256"}, {"name": "resolvers", "type": "address[]", "internalType": "address[]"}, {"name": "resolved", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "getPredictions", "inputs": [{"name": "_offset", "type": "uint256", "internalType": "uint256"}, {"name": "_limit", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple[]", "internalType": "struct IPrediction.Metadata[]", "components": [{"name": "polygonId", "type": "string", "internalType": "string"}, {"name": "polygonCid", "type": "string", "internalType": "string"}, {"name": "conditions", "type": "string[]", "internalType": "string[]"}, {"name": "outcomeSlot", "type": "uint256", "internalType": "uint256"}, {"name": "startDate", "type": "uint256", "internalType": "uint256"}, {"name": "endDate", "type": "uint256", "internalType": "uint256"}, {"name": "resolvers", "type": "address[]", "internalType": "address[]"}, {"name": "resolved", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "getPredictionsByPolygon", "inputs": [{"name": "_polygonHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "tuple[]", "internalType": "struct IPrediction.Metadata[]", "components": [{"name": "polygonId", "type": "string", "internalType": "string"}, {"name": "polygonCid", "type": "string", "internalType": "string"}, {"name": "conditions", "type": "string[]", "internalType": "string[]"}, {"name": "outcomeSlot", "type": "uint256", "internalType": "uint256"}, {"name": "startDate", "type": "uint256", "internalType": "uint256"}, {"name": "endDate", "type": "uint256", "internalType": "uint256"}, {"name": "resolvers", "type": "address[]", "internalType": "address[]"}, {"name": "resolved", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "getResolvablePredictions", "inputs": [{"name": "_offset", "type": "uint256", "internalType": "uint256"}, {"name": "_limit", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple[]", "internalType": "struct IPrediction.Metadata[]", "components": [{"name": "polygonId", "type": "string", "internalType": "string"}, {"name": "polygonCid", "type": "string", "internalType": "string"}, {"name": "conditions", "type": "string[]", "internalType": "string[]"}, {"name": "outcomeSlot", "type": "uint256", "internalType": "uint256"}, {"name": "startDate", "type": "uint256", "internalType": "uint256"}, {"name": "endDate", "type": "uint256", "internalType": "uint256"}, {"name": "resolvers", "type": "address[]", "internalType": "address[]"}, {"name": "resolved", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "getTotalPredictions", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getTotalPredictionsByPolygon", "inputs": [{"name": "_polygonHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "revokeResolver", "inputs": [{"name": "_resolver", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setMinimalInitialLiquidity", "inputs": [{"name": "_amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "Factory_MinimalInitialLiquiditySet", "inputs": [{"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Factory_PredictionCreated", "inputs": [{"name": "prediction", "type": "address", "indexed": true, "internalType": "address"}, {"name": "polygonHash", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "conditions", "type": "string[]", "indexed": false, "internalType": "string[]"}, {"name": "startDate", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "endDate", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "outcomeSlot", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "resolvers", "type": "address[]", "indexed": false, "internalType": "address[]"}, {"name": "creator", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Factory_ResolverAdded", "inputs": [{"name": "resolver", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Factory_ResolverRekoved", "inputs": [{"name": "resolver", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "ECDSAInvalidSignature", "inputs": []}, {"type": "error", "name": "ECDSAInvalidSignatureLength", "inputs": [{"name": "length", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ECDSAInvalidSignatureS", "inputs": [{"name": "s", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "Factory_ConditionsMustMatchOutcomeSlot", "inputs": []}, {"type": "error", "name": "Factory_InsufficientAllowance", "inputs": [{"name": "allowance", "type": "uint256", "internalType": "uint256"}, {"name": "required", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "Factory_InsufficientAmount", "inputs": []}, {"type": "error", "name": "Factory_InsufficientInitialLiquidity", "inputs": []}, {"type": "error", "name": "Factory_InvalidAddress", "inputs": []}, {"type": "error", "name": "Factory_InvalidPredictionDates", "inputs": []}, {"type": "error", "name": "Factory_InvalidSignature", "inputs": []}, {"type": "error", "name": "Factory_NeedResolver", "inputs": []}, {"type": "error", "name": "Factory_PredictionAlreadyRegistered", "inputs": []}, {"type": "error", "name": "Factory_SignatureAlreadyUsed", "inputs": []}, {"type": "error", "name": "Factory_UnsafeResolver", "inputs": []}, {"type": "error", "name": "FailedDeployment", "inputs": []}, {"type": "error", "name": "InsufficientBalance", "inputs": [{"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}]}
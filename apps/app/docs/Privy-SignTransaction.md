Ethereum
Sign a transaction
React
React Native
Swift
Android
Unity
Flutter
NodeJS
REST API
To send a transaction from a wallet using the React SDK, use the signTransaction method from the useSignTransaction hook:

Copy

Ask AI
signTransaction: (input: UnsignedTransactionRequest, options?: SendTransactionOptions) => Promise<{ signature: HexString }>
​
Usage

Copy

Ask AI
import {useSignTransaction} from '@privy-io/react-auth';
const {signTransaction} = useSignTransaction();
signTransaction({
to: '******************************************',
value: 100000
});
​
Parameters
​
input
UnsignedTransactionRequestrequired
The details of the transaction to sign.

​
options.uiOptions
SendTransactionModalUIOptions
The options for the UI of the send transaction modal. Learn more.

To hide confirmation modals, set options.uiOptions.showWalletUIs to false. Learn more about configuring modal prompts here.

​
options.fundWalletConfig
FundWalletConfig
The configuration for funding the wallet.

​
options.address
string
The address of the wallet to use for signing the transaction.

​
Returns
​
signature
HexString
The signed transaction hash.

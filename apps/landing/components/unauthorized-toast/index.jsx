'use client'

import { useEffect, useRef, useState } from 'react'
import cn from 'clsx'
import dynamic from 'next/dynamic'
import { useAudioDebounced } from '~/hooks/use-audio'
import s from './unauthorized-toast.module.css'

const ToastBorderLeft = dynamic(() => import('~/assets/svg/toast-border-left.svg'), { ssr: false })
const ToastBorderRight = dynamic(() => import('~/assets/svg/toast-border-right.svg'), { ssr: false })

export function UnauthorizedToast({ onVisibilityChange }) {
  const [isVisible, setIsVisible] = useState(false)
  const [isAnimatingOut, setIsAnimatingOut] = useState(false)
  const toastRef = useRef(null)
  const timeoutRef = useRef(null)
  const { play: playSound } = useAudioDebounced(100)

  const checkSession = async () => {
    try {
      const response = await fetch('/api/session')
      const data = await response.json()
      return data.value
    } catch (error) {
      console.error('Failed to check session:', error)
      return null
    }
  }

  const resetSession = async () => {
    try {
      await fetch('/api/session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ value: 'LOGIN' }),
      })
    } catch (error) {
      console.error('Failed to reset session:', error)
    }
  }

  const handleClose = async () => {
    playSound({ soundId: 'Sfx_UI_Click_Generic', volume: 0.4 })
    setIsAnimatingOut(true)

    setTimeout(async () => {
      setIsVisible(false)
      setIsAnimatingOut(false)
      await resetSession()
      onVisibilityChange?.(false)
    }, 300)
  }

  const handleAutoClose = async () => {
    setIsAnimatingOut(true)

    setTimeout(async () => {
      setIsVisible(false)
      setIsAnimatingOut(false)
      await resetSession()
      onVisibilityChange?.(false)
    }, 300)
  }

  useEffect(() => {
    const checkForUnauthorizedSession = async () => {
      const session = await checkSession()
      
      if (session === 'UNAUTHORIZED_LOGIN_ATTEMPT') {
        setIsVisible(true)
        onVisibilityChange?.(true)
        playSound({ soundId: 'Sfx_UI_AccessDenied', volume: 0.6 })

        // Auto-close after 5 seconds
        timeoutRef.current = setTimeout(handleAutoClose, 5000)
      }
    }

    checkForUnauthorizedSession()

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  // if (!isVisible) return null

  return (
    <div
      className={cn(s.toast, isAnimatingOut && s.animatingOut)}
      ref={toastRef}
    >
      <div className={s.border}>
        <div className={s.stroke} />
        <ToastBorderRight className={s.right} />
        <ToastBorderLeft className={s.left} />
      </div>

      <div className={s.content}>
        <div className={s.icon}>⚠️</div>
        <div className={s.text}>
          <p className={cn('ps uppercase', s.title)}>Access Denied</p>
          <p className={cn('pxs', s.message)}>
            You don't have permission to access the app. Please use a valid invite code.
          </p>
        </div>

        <button
          className={s.closeButton}
          onClick={handleClose}
          aria-label="Close notification"
        >
          ✕
        </button>
      </div>
    </div>
  )
}

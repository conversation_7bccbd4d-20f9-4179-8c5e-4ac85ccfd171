.toast {
  position: fixed;
  top: var(--layout-margin);
  right: var(--layout-margin);
  z-index: 1000;
  pointer-events: auto;
  
  width: desktop-vw(320px);
  min-height: desktop-vw(80px);
  
  @include-media 'mobile' {
    width: mobile-vw(280px);
    min-height: mobile-vw(80px);
    top: mobile-vw(20px);
    right: mobile-vw(20px);
  }

  /* Use same clip-path as secondary buttons */
  --clip-cut-size: desktop-vw(8px);
  
  @include-media 'mobile' {
    --clip-cut-size: mobile-vw(8px);
  }

  clip-path: polygon(
    var(--clip-cut-size) 0,
    100% 0,
    100% calc(100% - var(--clip-cut-size)),
    calc(100% - var(--clip-cut-size)) 100%,
    0 100%,
    0 var(--clip-cut-size)
  );

  /* Animation */
  animation: slideInFromRight 0.4s var(--gleasing) forwards;
  
  &.animatingOut {
    animation: slideOutToRight 0.3s var(--gleasing) forwards;
  }
}

.border {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  
  clip-path: polygon(
    var(--clip-cut-size) 0,
    100% 0,
    100% calc(100% - var(--clip-cut-size)),
    calc(100% - var(--clip-cut-size)) 100%,
    0 100%,
    0 var(--clip-cut-size)
  );
}

.stroke {
  position: absolute;
  inset: 0;
  border: desktop-vw(1px) solid var(--red);
  background-color: rgba(255, 99, 99, 0.1);
  backdrop-filter: blur(8px);
  
  @include-media 'mobile' {
    border: mobile-vw(1px) solid var(--red);
  }
}

.content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: flex-start;
  gap: desktop-vw(12px);
  padding: desktop-vw(16px);
  
  @include-media 'mobile' {
    gap: mobile-vw(12px);
    padding: mobile-vw(16px);
  }
}

.icon {
  font-size: desktop-vw(20px);
  line-height: 1;
  flex-shrink: 0;
  margin-top: desktop-vw(2px);
  
  @include-media 'mobile' {
    font-size: mobile-vw(18px);
    margin-top: mobile-vw(2px);
  }
}

.text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: desktop-vw(4px);
  
  @include-media 'mobile' {
    gap: mobile-vw(4px);
  }
}

.title {
  color: var(--red);
  font-weight: 600;
  margin: 0;
}

.message {
  color: var(--white);
  margin: 0;
  line-height: 1.4;
}

.closeButton {
  position: relative;
  flex-shrink: 0;
  width: desktop-vw(24px);
  height: desktop-vw(24px);
  border: none;
  background: none;
  color: var(--white);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: desktop-vw(14px);
  line-height: 1;
  transition: color 0.2s var(--gleasing);
  
  @include-media 'mobile' {
    width: mobile-vw(24px);
    height: mobile-vw(24px);
    font-size: mobile-vw(14px);
  }
  
  &:hover {
    color: var(--red);
  }
}

/* Animations */
@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutToRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

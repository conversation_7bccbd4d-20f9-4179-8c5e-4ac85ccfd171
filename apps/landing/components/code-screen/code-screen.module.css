.codeScreen {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: desktop-vw(33px);
  color: #ffffff;

  @include-media ('mobile') {
    gap: mobile-vw(33px);
    width: columns(6);
    margin-inline: auto;
  }

  &:not([data-modal="code"]) {
    .logo {
      pointer-events: none !important;
    }

    opacity: 0;
    pointer-events: none;
  }

  &[data-modal="code"] {
    .form {
      .input {
        .input-frame {
          path:first-of-type {
            transition-delay: calc(500ms + var(--index) * 100ms);
            stroke-dashoffset: 0;
          }
        }
      }

      .separator {
        .item {
          transition-delay: calc(500ms + var(--separator-index) * 75ms);
          opacity: 1;

          @include-media ('mobile') {
            transition-delay: calc(500ms + var(--separator-index) * 25ms);
          }
        }
      }
    }
  }

  .logo {
    position: absolute;
    inset: 0;
  }

  .title,
  .description {
    display: grid;
    grid-template-columns: 1fr;

    .enter,
    .access,
    .error {
      grid-column-start: 1;
      grid-row-start: 1;
      text-align: center;
    }
  }

  .title {
    .enter,
    .access,
    .error {
      @include-media ('mobile') {
        width: columns(5);
      }
    }

    .enter {
      color: var(--green);
    }

    .access {
      color: var(--green);
    }

    .error {
      color: var(--red);
    }
  }

  .description {
    .enter,
    .access,
    .error {
      @include-media ('mobile') {
        width: columns(4);
      }
    }
  }

  .alternateAccess {
    text-align: center;
    margin-top: desktop-vw(-15px);
    margin-bottom: desktop-vw(15px);

    @include-media ('mobile') {
      margin-top: mobile-vw(-15px);
      margin-bottom: mobile-vw(15px);
    }

    .alternateLink {
      background: none;
      border: none;
      cursor: pointer;
      font-size: 0.7rem;
      font-family: inherit;
      padding: 0;
      margin: 0;
      color: var(--green);
      transition: opacity 200ms var(--gleasing);
      opacity: 0.3;
      text-decoration: underline;

      &:hover {
        opacity: 1;
      }
    }
  }

  .close {
    position: absolute;
    top: 0;
    right: 0;

    svg {
      transition: stroke 300ms var(--gleasing) 250ms;
    }

    &[data-success="false"] {
      svg {
        path {
          stroke: var(--red);
        }
      }
    }

    width: desktop-vw(113px);

    @include-media ('mobile') {
      width: mobile-vw(113px);
    }
  }

  .form {
    display: flex;
    column-gap: mobile-vw(12px);
    --color: var(--green);

    @include-media ('mobile') {
      width: columns(6);
      flex-wrap: wrap;
      justify-content: center;
      row-gap: mobile-vw(12px);
    }

    @include-media ('desktop') {
      margin-top: desktop-vw(15px);
      height: desktop-vw(64px);
      column-gap: desktop-vw(12px);
    }

    &[data-success="false"] {
      --color: var(--red);

      .input {
        .glyph {
          path:first-of-type {
            transition-delay: 250ms !important;
          }

          path:last-of-type,
          circle {
            transition-delay: 250ms !important;
          }
        }
      }
    }

    &[data-success="false"],
    &[data-success="true"],
    &.pending {
      .input {
        .glyph {
          opacity: 1;
        }

        input {
          opacity: 0;
        }
      }
    }

    &.pending {
      .separator {
        .item {
          animation-duration: 0.5s;
        }
      }
    }

    .input {
      display: grid;
      grid-template-columns: 1fr;
      position: relative;
      width: mobile-vw(64px);
      height: mobile-vw(64px);
      background-color: var(--black);

      @include-media ('desktop') {
        width: desktop-vw(64px);
        height: desktop-vw(64px);
      }

      &.active {
        input {
          color: var(--white) !important;
        }

        .input-frame {
          path {
            transition-delay: 0s !important;
            stroke: var(--grey);
          }
        }
      }
    }

    input,
    .input-frame,
    .glyph {
      grid-column-start: 1;
      grid-row-start: 1;
    }

    .glyph,
    input {
      transition: opacity 300ms var(--gleasing) calc(var(--index) * 100ms);
    }

    input {
      position: relative;
      width: 100%;
      height: 100%;
      text-align: center;
      outline: none;

      @include-media ('mobile') {
        font-size: 16px !important;
      }
    }

    .input-frame {
      pointer-events: none;

      path {
        transition-property: stroke-dashoffset, stroke-dasharray, stroke;
        transition-duration: 300ms;
        transition-timing-function: var(--gleasing);
      }

      path:first-of-type {
        stroke-dasharray: 300;
        stroke-dashoffset: 300;
      }

      path:not(:first-of-type) {
        stroke: var(--color);
      }

      path:nth-of-type(2),
      path:nth-of-type(3) {
        stroke-dasharray: 40;
        stroke-dashoffset: 40;
      }

      path:nth-of-type(4),
      path:nth-of-type(5) {
        stroke-dasharray: 12;
        stroke-dashoffset: 12;
      }

      &:has(+ input:focus) {
        path:nth-of-type(2),
        path:nth-of-type(3),
        path:nth-of-type(4),
        path:nth-of-type(5) {
          stroke-dashoffset: 0;
        }
      }
    }

    .glyph {
      pointer-events: none;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      width: 100%;
      height: 100%;

      svg {
        position: relative;
        width: mobile-vw(28px);

        @include-media ('desktop') {
          width: desktop-vw(28px);
          aspect-ratio: 1;
        }
      }

      path:first-of-type {
        fill: var(--color);
      }

      path:last-of-type,
      circle {
        stroke: var(--color);
      }
    }

    .separator {
      display: flex;
      width: 100%;

      @include-media ('desktop') {
        margin-inline: desktop-vw(12px);
        width: desktop-vw(16px);
        row-gap: desktop-vw(6px);
        flex-direction: column;
      }

      @include-media ('mobile') {
        width: mobile-vw(216px);
        height: mobile-vw(16px);
        column-gap: mobile-vw(8px);
        overflow: hidden;
      }

      .item {
        width: 100%;
        height: desktop-vw(1px);
        background-color: var(--darkGrey);
        flex-grow: 0;
        flex-shrink: 0;

        @include-media ('mobile') {
          height: 100%;
          width: mobile-vw(1px);
        }

        animation-delay: calc((var(--separator-index) + 1) * 0.2s);
        opacity: 0;
        transition: opacity 300ms var(--gleasing);
      }
    }
  }
}

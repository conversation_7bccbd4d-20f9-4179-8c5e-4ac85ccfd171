'use client'

import cn from 'clsx'
import dynamic from 'next/dynamic'
import { useRouter } from 'next/navigation'
import {
  useActionState,
  useCallback,
  useEffect,
  useId,
  useRef,
  useState,
} from 'react'
import { useModal } from '~/app/(pages)/(components)/modal'
import { Logo } from '~/app/(pages)/(components)/navigation/logo'
import { useAudioDebounced } from '~/hooks/use-audio'
import { storeSuccessfulReferralAuth } from '~/libs/auth-storage'
import { isEmptyArray } from '~/libs/utils'
import { SecondaryButton } from '../button'
import { Frames } from '../frames'
import { ScrambleText } from '../scramble/'
import { codeScreenAction } from './action'
import s from './code-screen.module.css'

const InputFrameSVG = dynamic(() => import('~/assets/svg/input-frame.svg'), {
  ssr: false,
})

const initialValue = {
  code: new Array(6).fill(''),
  success: null,
}

export function CodeScreen() {
  const [formKey, setFormKey] = useState(null)
  const { current: currentModal } = useModal()

  return (
    <div className={s.codeScreen} data-modal={currentModal}>
      <Form key={formKey} setKey={setFormKey} />
      <Frames direction="horizontal" color="darkGrey" />
    </div>
  )
}

function Form({ setKey }) {
  const id = useId()
  const hasOpenedRef = useRef(false)
  // Text refs
  const enterTextRef = useRef(null)
  const enterDescriptionRef = useRef(null)
  const successTextRef = useRef(null)
  const successDescriptionRef = useRef(null)
  const errorTextRef = useRef(null)
  const errorDescriptionRef = useRef(null)
  // Timeouts refs
  const onResponseTimeoutRef = useRef(null)
  const focusTimeoutRef = useRef(null)
  const cleanFormTimeoutRef = useRef(null)
  // Sound
  const loopSoundRef = useRef(null)
  const { play: playSound, stop: stopSound } = useAudioDebounced(0)
  // Form
  const formRef = useRef(null)
  const inputsRefs = useRef([])
  const router = useRouter()
  const { current: currentModal, close: closeModal, open: openModal } = useModal()
  const [state, formAction, isPending] = useActionState(
    codeScreenAction,
    initialValue
  )

  const onResponse = useCallback(({ targets, soundId }) => {
    enterTextRef.current?.hide({
      duration: 0.25,
      direction: 'left',
      onComplete: () => {
        targets.text?.show({
          duration: 0.5,
          direction: 'right',
          onStart: () => {
            playSound({ soundId })
          },
        })
      },
    })

    enterDescriptionRef.current?.hide({
      duration: 0.25,
      direction: 'left',
      onComplete: () => {
        targets.description?.show({
          duration: 0.5,
          direction: 'right',
        })
      },
    })
  }, [])

  const handleChange = useCallback(({ target }) => {
    const { form } = target
    const formData = new FormData(form)
    const code = formData?.getAll('code') ?? []

    if (isEmptyArray(code)) return

    const nextIndex = code.findIndex(
      (value, index) => value === '' && code[index - 1] !== ''
    )

    if (nextIndex !== -1) {
      inputsRefs.current[nextIndex].focus()
    }

    if (form.checkValidity()) {
      for (const input of inputsRefs.current) {
        input?.blur()
      }

      form.requestSubmit()
      playSound({
        soundId: 'Sfx_UI_GreenCircles',
        rate: 0.75,
        onComplete: () => {
          loopSoundRef.current = playSound({
            soundId: 'Sfx_UI_DataProcessing_Loop',
            loop: true,
          })
        },
      })
    }
  }, [])

  const onOpen = useCallback(() => {
    hasOpenedRef.current = true

    enterTextRef.current?.show({
      direction: 'random',
      onStart: () => {
        playSound({
          soundId: 'Sfx_UI_GreenText_Appear',
          rate: 1.75,
        })
      },
    })

    enterDescriptionRef.current?.show({
      direction: 'random',
      onComplete: () => {
        focusTimeoutRef.current = setTimeout(() => {
          inputsRefs.current?.[0]?.focus()
          playSound({ soundId: 'Sfx_UI_FirstSquare_Highlight' })
        }, 200)
      },
    })
  }, [])

  const onClose = useCallback(() => {
    hasOpenedRef.current = false

    cleanFormTimeoutRef.current = setTimeout(() => {
      setKey(id)
    }, 1000)

    stopSound({ id: loopSoundRef.current })
    clearTimeout(focusTimeoutRef.current)
    clearTimeout(onResponseTimeoutRef.current)
    focusTimeoutRef.current = null
    onResponseTimeoutRef.current = null
  }, [])

  useEffect(() => {
    // Success
    if (state.success === true) {
      onResponse({
        targets: {
          text: successTextRef.current,
          description: successDescriptionRef.current,
        },
        soundId: 'Sfx_UI_AccessGranted',
      })

      onResponseTimeoutRef.current = setTimeout(() => {
        if (state.storeAuthState) {
          storeSuccessfulReferralAuth()
        }
        router.push(state.redirect)
        setKey(id)
      }, 4000)
    }

    // Error
    if (state.success === false) {
      onResponse({
        targets: {
          text: errorTextRef.current,
          description: errorDescriptionRef.current,
        },
        soundId: 'Sfx_UI_AccessDenied',
      })

      onResponseTimeoutRef.current = setTimeout(() => {
        setKey(id)
        inputsRefs.current[0].focus()
      }, 4000)
    }

    return () => {
      clearTimeout(onResponseTimeoutRef.current)
      onResponseTimeoutRef.current = null
    }
  }, [state])

  useEffect(() => {
    if (hasOpenedRef.current && currentModal !== 'code') {
      onClose()

      return () => {
        clearTimeout(cleanFormTimeoutRef.current)
        cleanFormTimeoutRef.current = null
      }
    }

    if (currentModal === 'code') {
      onOpen()
    }

    return () => {
      clearTimeout(focusTimeoutRef.current)
      focusTimeoutRef.current = null
      cleanFormTimeoutRef.current = null
    }
  }, [currentModal])

  useEffect(() => {
    // On pending
    if (isPending) return

    stopSound({ id: loopSoundRef.current })
  }, [isPending])

  return (
    <>
      <SecondaryButton
        className={cn('ps uppercase', s.close)}
        onClick={() => {
          closeModal()
          playSound({ soundId: 'Sfx_UI_Close', volume: 0.4 })
        }}
        data-success={state.success}
      >
        {formData.button}
      </SecondaryButton>
      <Logo className={s.logo} />
      <div className={cn('h3', s.title)} data-success={state.success}>
        <ScrambleText className={s.enter} ref={enterTextRef} initState="hide">
          {formData.title.enter}
        </ScrambleText>
        <ScrambleText
          className={s.access}
          ref={successTextRef}
          initState="hide"
        >
          {formData.title.access}
        </ScrambleText>
        <ScrambleText className={s.error} ref={errorTextRef} initState="hide">
          {formData.title.error}
        </ScrambleText>
      </div>
      <div className={cn('ps-text uppercase', s.description)}>
        <ScrambleText
          className={s.enter}
          ref={enterDescriptionRef}
          initState="hide"
        >
          {formData.description.enter}
        </ScrambleText>
        <ScrambleText
          className={s.access}
          ref={successDescriptionRef}
          initState="hide"
        >
          {formData.description.access}
        </ScrambleText>
        <ScrambleText
          className={s.error}
          ref={errorDescriptionRef}
          initState="hide"
        >
          {formData.description.error}
        </ScrambleText>
      </div>
      <form
        className={cn(s.form, isPending && s.pending)}
        data-success={state.success}
        action={formAction}
        onChange={handleChange}
        ref={formRef}
      >
        {new Array(3).fill(false).map((_, index) => (
          <Input
            // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
            key={`first-${index}`}
            index={index}
            defaultValue={state.code}
            ref={(node) => {
              inputsRefs.current[index] = node
            }}
            badge={badges[`Badge${index + 1}`]}
            inputsRefs={inputsRefs.current}
          />
        ))}
        <div className={cn('desktop-only', s.separator)}>
          {new Array(10).fill('').map((_, index) => (
            <div
              // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
              key={index}
              className={cn(s.item, 'animation-colorCascade')}
              style={{
                '--separator-index': index,
              }}
            />
          ))}
        </div>
        <div className={cn('mobile-only', s.separator)}>
          {new Array(30).fill('').map((_, index) => (
            <div
              // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
              key={index}
              className={cn(
                s.item,
                isPending && s.pending,
                'animation-colorCascade'
              )}
              style={{
                '--separator-index': index,
              }}
            />
          ))}
        </div>
        {new Array(3).fill(false).map((_, index) => (
          <Input
            // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
            key={`second-${index}`}
            index={index + 3}
            defaultValue={state.code}
            ref={(node) => {
              inputsRefs.current[index + 3] = node
            }}
            badge={badges[`Badge${index + 4}`]}
            inputsRefs={inputsRefs.current}
          />
        ))}
      </form>

      <div className={cn('ps-text', s.alternateAccess)}>
        <button 
          type="button" 
          onClick={() => {
            playSound({ soundId: 'Sfx_UI_Close', volume: 0.4 })
            if (typeof closeModal === 'function') closeModal()
            if (typeof openModal === 'function') openModal('waitlist')
          }}
          className={s.alternateLink}
        >
          Enter waitlist here
        </button>
      </div>
    </>
  )
}

function Input({ index, defaultValue, ref, badge, inputsRefs }) {
  const [active, setActive] = useState(false)
  const Tag = badge
  const { play: playSound } = useAudioDebounced(100)
  const CODE_LENGTH = 6

  const handlePastedCode = useCallback((e) => {
    const pasted = e.clipboardData.getData('text').replace(/\s/g, '')
          if (pasted.length === CODE_LENGTH && inputsRefs && inputsRefs.length === CODE_LENGTH) {
            e.preventDefault()
            for (let i = 0; i < CODE_LENGTH; i++) {
              if (inputsRefs[i]) {
                inputsRefs[i].value = pasted[i] || ''
                const event = new Event('input', { bubbles: true })
                inputsRefs[i].dispatchEvent(event)
              }
            }

            if (inputsRefs[5]) {
              inputsRefs[5].focus()
            }

            setTimeout(() => {
              const form = inputsRefs[0]?.form
              if (form) {
                if (form.requestSubmit) {
                  form.requestSubmit()
                } else {
                  const event = new Event('change', { bubbles: true })
                  form.dispatchEvent(event)
                }
              }
            }, 0)
          }
  }, [])

  return (
    <div
      className={cn(s.input, active && s.active)}
      style={{
        '--index': index,
      }}
    >
      <InputFrameSVG className={s['input-frame']} />
      <input
        ref={ref}
        type="text"
        inputMode="text"
        pattern="[A-Za-z0-9]"
        maxLength="1"
        minLength="1"
        required
        id={`code-${index}`}
        name="code"
        className="h4 green uppercase"
        defaultValue={defaultValue[index] ?? ''}
        onChange={({ target }) => {
          setActive(target.value.length > 0)

          if (target.value.length > 0) {
            playSound({ soundId: 'Sfx_UI_Type_Character' })
          }
        }}
        onKeyDown={(e) => {
          if (e.key === 'Backspace' && e.target.value === '') {
            if (inputsRefs && inputsRefs[index - 1]) {
              inputsRefs[index - 1].focus()
            }
          }
        }}
        onPaste={handlePastedCode}
      />
      <div className={s.glyph}>
        <Tag />
      </div>
    </div>
  )
}

const badges = {
  Badge1: dynamic(() => import('~/assets/svg/badge1.svg'), {
    ssr: false,
  }),
  Badge2: dynamic(() => import('~/assets/svg/badge2.svg'), {
    ssr: false,
  }),
  Badge3: dynamic(() => import('~/assets/svg/badge3.svg'), {
    ssr: false,
  }),
  Badge4: dynamic(() => import('~/assets/svg/badge4.svg'), {
    ssr: false,
  }),
  Badge5: dynamic(() => import('~/assets/svg/badge5.svg'), {
    ssr: false,
  }),
  Badge6: dynamic(() => import('~/assets/svg/badge6.svg'), {
    ssr: false,
  }),
}

const formData = {
  button: 'close',
  title: {
    enter: 'ENTER YOUR INVITE CODE',
    access: 'ACCESS GRANTED',
    error: 'ACCESS DENIED',
  },
  description: {
    enter: 'use your 6-digit code to access the platform',
    access: 'Welcome to Ibicash',
    error: 'Please check the code and try again',
  },
}

{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "editor.defaultFormatter": "biomejs.biome", "typescript.suggest.autoImports": true, "javascript.suggest.autoImports": true, "workbench.editor.customLabels.patterns": {"**/app/**/page.js": "${dirname(1)}/${dirname} <page>", "**/app/**/layout.js": "${dirname(1)}/${dirname} <layout>", "**/app/api/**/route.js": "${dirname(1)}/${dirname} <route>"}, "files.associations": {"*.json": "jsonc", "*.css": "css"}, "css.lint.validProperties": ["user-drag"], "css.lint.unknownAtRules": "ignore", "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "files.eol": "\n", "[css]": {"editor.defaultFormatter": "biomejs.biome"}}
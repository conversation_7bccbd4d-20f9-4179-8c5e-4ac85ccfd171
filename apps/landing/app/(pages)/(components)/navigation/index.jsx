'use client'

import { types } from '@theatre/core'
import { track } from '@vercel/analytics'
import cn from 'clsx'
import dynamic from 'next/dynamic'
import { useEffect, useRef, useState } from 'react'
import TempGlyphs from '~/assets/svg/tmp-glyphs.svg'
import {
  PrimaryButton,
  SecondaryButton,
  SoundButton,
} from '~/components/button'
import { useAudioDebounced } from '~/hooks/use-audio'
import { useSheet } from '~/libs/theatre'
import { useTheatre } from '~/libs/theatre/hooks/use-theatre'
import { useModal } from '../modal'
import { UnauthorizedToast } from '~/components/unauthorized-toast'
import { Logo } from './logo'
import s from './navigation.module.css'

const Circle = dynamic(() => import('~/assets/svg/circle.svg'), { ssr: false })
const DashedCircle = dynamic(() => import('~/assets/svg/dashed-circle.svg'), {
  ssr: false,
})

export function Navigation() {
  return (
    <nav className={s.nav}>
      <Circles />
      <div className={cn('layout-grid-inner', s.top)}>
        <Logo />
        <Stats />
        <CTAs className="desktop-only" />
      </div>
      <div className={cn('layout-grid-inner', s.bottom)}>
        <Glyphs className="desktop-only" />
        <CTAs className="mobile-only" />
        <SoundCta />
      </div>
      <UnauthorizedToast />
    </nav>
  )
}

function Circles() {
  const dashedRef = useRef(null)
  const filledRef = useRef(null)
  const introSheet = useSheet('intro')

  useTheatre(
    introSheet,
    'solid circle',
    {
      opacity: types.number(0, { range: [0, 1], nudgeMultiplier: 0.01 }),
      scale: types.number(1, { range: [0, 1.5], nudgeMultiplier: 0.01 }),
    },
    {
      onValuesChange: ({ opacity, scale }) => {
        filledRef.current?.style?.setProperty('opacity', opacity)
        filledRef.current?.style?.setProperty(
          'transform',
          `translate(-50%, -50%) scale(${scale})`
        )
      },
    }
  )

  useTheatre(
    introSheet,
    'dashed circle',
    {
      opacity: types.number(0, { range: [0, 1], nudgeMultiplier: 0.01 }),
      scale: types.number(1, { range: [0, 1.5], nudgeMultiplier: 0.01 }),
    },
    {
      onValuesChange: ({ opacity, scale }) => {
        dashedRef.current?.style?.setProperty('opacity', opacity)
        dashedRef.current?.style?.setProperty(
          'transform',
          `translate(-50%, -50%)  scale(${scale})`
        )

        if (opacity === 1) {
          dashedRef.current?.classList?.add('rotate-centered-infinite')
        } else {
          dashedRef.current?.classList?.remove('rotate-centered-infinite')
        }
      },
    }
  )

  return (
    <>
      <div className={s.filled} ref={filledRef}>
        <Circle />
      </div>
      <div className={cn(s.dashed)} ref={dashedRef}>
        <DashedCircle />
      </div>
    </>
  )
}

function CTAs({ className }) {
  const ctasRef = useRef(null)
  const introSheet = useSheet('intro')
  const { open: openModal } = useModal()
  const { play: playSound } = useAudioDebounced(100)
  const [buttonState, setButtonState] = useState('login')

  useTheatre(
    introSheet,
    'ctas',
    {
      opacity: types.number(0, { range: [0, 1], nudgeMultiplier: 0.01 }),
    },
    {
      onValuesChange: ({ opacity }) => {
        ctasRef.current?.style?.setProperty('opacity', opacity)
      },
    }
  )

  const getSession = async () => {
    const response = await fetch('/api/session')
    const data = await response.json()
    return data.value
  }
  
  async function getButtonState() {
    const session = await getSession()
    
    if (session === 'LOGGED_IN') {
      return 'enterWorld'
    }
  
    return 'login'
  }

  useEffect(() => {
    const updateButtonState = async () => {
      const buttonState = await getButtonState()
      setButtonState(buttonState)
    }

    updateButtonState()
  }, [])

  const handleSecondaryButtonClick = () => {
    playSound({ soundId: 'Sfx_UI_Click_Generic', volume: 0.4 })

    track('Button state', { state: buttonState })

    if (buttonState === 'enterWorld') {
      window.location.href = 'https://app.ibi.cash/claim'
      track('Enter World button clicked')
    } else if (buttonState === 'login') {
      window.location.href = 'https://app.ibi.cash'
      track('Login button clicked')
    }
  }

  const buttonStateText = () => {
    if (buttonState === 'enterWorld') {
      return 'ENTER WORLD'
    } else if (buttonState === 'login') {
      return 'LOGIN'
    } else {
      return 'LOGIN'
    }
  }

  return (
    <div className={cn(s.ctas, className)} ref={ctasRef}>
      <PrimaryButton
        className="ps black uppercase"
        onClick={() => {
          playSound({ soundId: 'Sfx_UI_Click_Generic', volume: 0.4 })
          openModal('code')
          track('Redeem button clicked')
        }}
      >
        redeem code
      </PrimaryButton>
      <SecondaryButton
        className="ps white uppercase"
        onClick={handleSecondaryButtonClick}
      >
        {buttonStateText()}
      </SecondaryButton>
    </div>
  )
}

function Stats({ className }) {
  const statsRef = useRef(null)
  const introSheet = useSheet('intro')

  useTheatre(
    introSheet,
    'stats',
    {
      opacity: types.number(0, { range: [0, 1], nudgeMultiplier: 0.01 }),
    },
    {
      onValuesChange: ({ opacity }) => {
        statsRef.current?.style?.setProperty('opacity', opacity)
      },
    }
  )

  return (
    <div className={cn(s.stats, s.red, className)} ref={statsRef}>
      <p className={cn('pxs uppercase grey', s.headline)}>market cap</p>
      <div className={s.values}>
        <p className="ps">$79.45M</p>
        <p className={cn(s.current, 'pxs')}>6.10%</p>
      </div>
    </div>
  )
}

function Glyphs({ className }) {
  const svgRef = useRef()
  const introSheet = useSheet('intro')

  useTheatre(
    introSheet,
    'glyphs',
    {
      opacity: types.number(0, { range: [0, 1], nudgeMultiplier: 0.01 }),
    },
    {
      onValuesChange: ({ opacity }) => {
        svgRef.current?.style?.setProperty('opacity', opacity)
      },
    }
  )

  return (
    <div className={cn(s.glyphs, className)}>
      <TempGlyphs ref={svgRef} />
    </div>
  )
}

function SoundCta() {
  const soundButtonRef = useRef(null)
  const introSheet = useSheet('intro')

  useTheatre(
    introSheet,
    'sound cta',
    { opacity: types.number(0, { range: [0, 1], nudgeMultiplier: 0.01 }) },
    {
      onValuesChange: ({ opacity }) => {
        soundButtonRef.current?.style?.setProperty('opacity', opacity)
      },
    }
  )

  return <SoundButton className={s.sound} ref={soundButtonRef} />
}

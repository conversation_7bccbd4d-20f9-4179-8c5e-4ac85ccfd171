'use client'

import cn from 'clsx'
import type { LenisOptions } from 'lenis'
import { usePathname } from 'next/navigation'
import { Suspense, useEffect } from 'react'
import { Cursor } from '~/components/cursor'
import { GlobalAudio } from '~/components/global-audio'
import { Loader } from '~/components/loader'
import { handleLogoutQueryParam } from '~/libs/auth-storage'
import { useStore } from '~/libs/store'
import { useSheet } from '~/libs/theatre'
import { useStudio } from '~/libs/theatre/hooks/use-studio'
import { Canvas } from '~/libs/webgl/components/canvas'
import type { themeNames } from '~/styles/config.mjs'
import { Lenis } from '../lenis'
import { Modal } from '../modal'
import { Navigation } from '../navigation'
import s from './wrapper.module.css'
interface WrapperProps extends React.HTMLAttributes<HTMLDivElement> {
  theme?: (typeof themeNames)[number]
  lenis?: boolean | LenisOptions
  webgl?: boolean | object
  loader?: boolean
}

export function Wrapper({
  children,
  theme = 'dark',
  className,
  lenis = true,
  webgl,
  loader = true,
  ...props
}: WrapperProps) {
  const pathname = usePathname()

  const setIntroCompleted = useStore((state) => state.setIntroCompleted)
  const introSheet = useSheet('intro')

  // biome-ignore lint/correctness/useExhaustiveDependencies: we need to trigger on path change
  useEffect(() => {
    document.documentElement.setAttribute('data-theme', theme)
  }, [pathname, theme])

  useEffect(() => {
    if (!loader && introSheet) {
      setIntroCompleted(true)
      introSheet.sequence.position = 1000
    }
  }, [setIntroCompleted, loader, introSheet])

  useEffect(() => {
    handleLogoutQueryParam()
  }, [])

  const isStudio = useStudio()

  return (
    <>
      {webgl && <Canvas root {...(typeof webgl === 'object' && webgl)} />}

      <Suspense>
        <Cursor>
          <GlobalAudio>
            <main
              className={cn(s.main, className)}
              style={{
                pointerEvents: isStudio ? 'none' : 'auto',
              }}
              {...props}
            >
              {children}
              <script>
                {`document.documentElement.setAttribute('data-theme', '${theme}');`}
              </script>
            </main>
            <Suspense>
              <Navigation />
            </Suspense>
            <Modal />
            {loader && <Loader />}
          </GlobalAudio>
        </Cursor>

        {lenis && (
          <Lenis root options={typeof lenis === 'object' ? lenis : {}} />
        )}
      </Suspense>
    </>
  )
}

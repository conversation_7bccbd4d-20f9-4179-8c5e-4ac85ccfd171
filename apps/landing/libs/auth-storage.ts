const STORAGE_KEYS = {
  HAS_USED_REFERRAL_CODE: 'hasUsedReferralCode',
  IS_CURRENTLY_AUTHENTICATED: 'isCurrentlyAuthenticated',
} as const;

/**
 * Sets the flag indicating user has successfully used a referral code
 * This value persists until manually cleared
 */
export function setHasUsedReferralCode(): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem(STORAGE_KEYS.HAS_USED_REFERRAL_CODE, 'true');
  }
}

/**
 * Sets the flag indicating user is currently authenticated in the app
 * This value is cleared on logout
 */
export function setIsCurrentlyAuthenticated(): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem(STORAGE_KEYS.IS_CURRENTLY_AUTHENTICATED, 'true');
  }
}

/**
 * Checks if user has ever used a referral code
 */
export function getHasUsedReferralCode(): boolean {
  if (typeof window === 'undefined') return false;
  return localStorage.getItem(STORAGE_KEYS.HAS_USED_REFERRAL_CODE) === 'true';
}

/**
 * Checks if user is currently authenticated
 */
export function getIsCurrentlyAuthenticated(): boolean {
  if (typeof window === 'undefined') return false;
  return localStorage.getItem(STORAGE_KEYS.IS_CURRENTLY_AUTHENTICATED) === 'true';
}

/**
 * Clears the current authentication state (called on logout)
 * Keeps the hasUsedReferralCode flag intact
 */
export function clearCurrentAuthentication(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(STORAGE_KEYS.IS_CURRENTLY_AUTHENTICATED);
  }
}

/**
 * Clears all authentication-related localStorage values
 * Use this for complete reset (rarely needed)
 */
export function clearAllAuthStorage(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(STORAGE_KEYS.HAS_USED_REFERRAL_CODE);
    localStorage.removeItem(STORAGE_KEYS.IS_CURRENTLY_AUTHENTICATED);
  }
}

/**
 * Stores both authentication flags when user successfully uses referral code
 */
export function storeSuccessfulReferralAuth(): void {
  setHasUsedReferralCode();
  setIsCurrentlyAuthenticated();
}

/**
 * Determines what button should be shown based on localStorage state
 * @returns 'waitlist' | 'enterWorld' | 'login'
 */
export function getButtonState(): 'enterWorld' | 'login' {
  const isAuthenticated = getIsCurrentlyAuthenticated();
  const hasUsedCode = getHasUsedReferralCode();

  if (hasUsedCode && !isAuthenticated) {
    return 'login';
  } else if (isAuthenticated || hasUsedCode) {
    return 'enterWorld';
  }

  return 'login';
}

/**
 * Handles logout query parameter logic
 * Checks for ?logout=true and clears authentication if present
 */
export function handleLogoutQueryParam(): void {
  if (typeof window === 'undefined') return;

  const urlParams = new URLSearchParams(window.location.search);
  const isLogout = urlParams.get('logout') === 'true';

  if (isLogout) {
    clearCurrentAuthentication();

    urlParams.delete('logout');
    const newUrl = urlParams.toString()
      ? `${window.location.pathname}?${urlParams.toString()}`
      : window.location.pathname;

    window.history.replaceState({}, '', newUrl);
  }
}
